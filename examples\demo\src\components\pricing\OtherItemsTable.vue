<template>
  <div class="other-items-table">
    <div class="table-toolbar">
      <div class="toolbar-left">
        <a-button type="primary" @click="addOtherItem">
          <template #icon>
            <PlusOutlined />
          </template>
          添加其他项目
        </a-button>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data-source="otherItems"
      :pagination="false"
      :scroll="{ x: 1000, y: 600 }"
      row-key="name"
      size="small"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <!-- 项目名称列 -->
        <template v-if="column.key === 'name'">
          <a-input
            v-model:value="record.name"
            @blur="onCellChange"
            size="small"
          />
        </template>
        
        <!-- 项目类型列 -->
        <template v-else-if="column.key === 'item_type'">
          <a-select
            v-model:value="record.item_type"
            @change="onCellChange"
            size="small"
            style="width: 100%;"
          >
            <a-select-option value="ProvisionalSum">暂列金额</a-select-option>
            <a-select-option value="ProfessionalEstimate">专业工程暂估价</a-select-option>
            <a-select-option value="DayWork">计日工</a-select-option>
            <a-select-option value="GeneralContractorFee">总承包服务费</a-select-option>
          </a-select>
        </template>
        
        <!-- 金额列 -->
        <template v-else-if="column.key === 'amount'">
          <a-input-number
            v-model:value="record.amount"
            @blur="onAmountChange"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <!-- 备注列 -->
        <template v-else-if="column.key === 'remark'">
          <a-textarea
            v-model:value="record.remark"
            @blur="onCellChange"
            :rows="2"
            size="small"
          />
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.key === 'actions'">
          <a-popconfirm
            title="确定删除这个其他项目吗？"
            @confirm="deleteOtherItem(record)"
          >
            <a-button type="link" size="small" danger>
              删除
            </a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>

    <!-- 汇总信息 -->
    <div class="summary-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card title="暂列金额" size="small">
            <a-statistic
              :value="getAmountByType('ProvisionalSum')"
              :precision="2"
              suffix="元"
              :value-style="{ fontSize: '16px' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card title="专业工程暂估价" size="small">
            <a-statistic
              :value="getAmountByType('ProfessionalEstimate')"
              :precision="2"
              suffix="元"
              :value-style="{ fontSize: '16px' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card title="计日工" size="small">
            <a-statistic
              :value="getAmountByType('DayWork')"
              :precision="2"
              suffix="元"
              :value-style="{ fontSize: '16px' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card title="总承包服务费" size="small">
            <a-statistic
              :value="getAmountByType('GeneralContractorFee')"
              :precision="2"
              suffix="元"
              :value-style="{ fontSize: '16px' }"
            />
          </a-card>
        </a-col>
      </a-row>
      
      <a-card title="其他项目费总计" size="small" style="margin-top: 16px;">
        <a-statistic
          :value="totalOtherCost"
          :precision="2"
          suffix="元"
          :value-style="{ color: '#1890ff', fontSize: '18px' }"
        />
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  unitProject: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update'])

// 表格列定义
const columns = [
  {
    title: '项目名称',
    key: 'name',
    width: 250
  },
  {
    title: '项目类型',
    key: 'item_type',
    width: 180
  },
  {
    title: '金额(元)',
    key: 'amount',
    width: 150,
    align: 'right'
  },
  {
    title: '备注',
    key: 'remark',
    width: 300
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right'
  }
]

// 计算属性
const otherItems = computed(() => {
  return props.unitProject?.other_items || []
})

const totalOtherCost = computed(() => {
  return otherItems.value.reduce((sum, item) => sum + (item.amount || 0), 0)
})

// 根据类型获取金额
const getAmountByType = (type) => {
  return otherItems.value
    .filter(item => item.item_type === type)
    .reduce((sum, item) => sum + (item.amount || 0), 0)
}

// 添加其他项目
const addOtherItem = () => {
  const newItem = {
    name: '',
    item_type: 'ProvisionalSum',
    amount: 0,
    remark: ''
  }
  
  const updatedProject = { ...props.unitProject }
  if (!updatedProject.other_items) {
    updatedProject.other_items = []
  }
  updatedProject.other_items.push(newItem)
  
  emitUpdate(updatedProject)
}

// 删除其他项目
const deleteOtherItem = (item) => {
  const updatedProject = { ...props.unitProject }
  const index = updatedProject.other_items.findIndex(i => i.name === item.name)
  if (index > -1) {
    updatedProject.other_items.splice(index, 1)
    emitUpdate(updatedProject)
    message.success('其他项目删除成功')
  }
}

// 单元格变化处理
const onCellChange = () => {
  emitUpdate(props.unitProject)
}

// 金额变化处理
const onAmountChange = () => {
  emitUpdate(props.unitProject)
}

// 发送更新事件
const emitUpdate = (updatedProject) => {
  // 重新计算费用汇总
  const otherCost = updatedProject.other_items?.reduce((sum, item) => sum + (item.amount || 0), 0) || 0
  
  if (!updatedProject.cost_summary) {
    updatedProject.cost_summary = {
      bill_cost: 0,
      measure_cost: 0,
      other_cost: 0,
      tax: 0,
      total_cost: 0
    }
  }
  
  updatedProject.cost_summary.other_cost = otherCost
  updatedProject.cost_summary.total_cost = 
    updatedProject.cost_summary.bill_cost + 
    updatedProject.cost_summary.measure_cost + 
    updatedProject.cost_summary.other_cost + 
    updatedProject.cost_summary.tax
  
  emit('update', updatedProject)
}
</script>

<style scoped>
.other-items-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.summary-section {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
}

:deep(.ant-input-number) {
  border: none;
  box-shadow: none;
}

:deep(.ant-input-number:hover) {
  border-color: #1890ff;
}

:deep(.ant-input-number:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-select) {
  border: none;
}

:deep(.ant-select:hover .ant-select-selector) {
  border-color: #1890ff;
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>
