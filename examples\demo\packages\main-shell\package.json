{"name": "main-shell", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "copy-dist": "node copy-dist.js", "preview": "vite preview"}, "dependencies": {"vue": "^3.5.13", "vue-router": "^4.5.1", "element-plus": "^2.10.2", "pinia": "^3.0.3"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.5", "@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0"}}