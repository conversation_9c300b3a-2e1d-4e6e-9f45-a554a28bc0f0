<template>
  <div class="cost-summary">
    <a-row :gutter="[16, 16]">
      <!-- 分部分项工程费 -->
      <a-col :span="8">
        <a-card title="分部分项工程费" size="small">
          <a-statistic
            :value="costSummary.bill_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#52c41a', fontSize: '18px' }"
          />
        </a-card>
      </a-col>

      <!-- 措施项目费 -->
      <a-col :span="8">
        <a-card title="措施项目费" size="small">
          <a-statistic
            :value="costSummary.measure_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#1890ff', fontSize: '18px' }"
          />
        </a-card>
      </a-col>

      <!-- 其他项目费 -->
      <a-col :span="8">
        <a-card title="其他项目费" size="small">
          <a-statistic
            :value="costSummary.other_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#722ed1', fontSize: '18px' }"
          />
        </a-card>
      </a-col>

      <!-- 税金 -->
      <a-col :span="8">
        <a-card title="税金" size="small">
          <a-statistic
            :value="costSummary.tax"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#fa8c16', fontSize: '18px' }"
          />
          <div style="margin-top: 8px;">
            <span>税率: </span>
            <a-input-number
              v-model:value="taxRate"
              @change="onTaxRateChange"
              :precision="2"
              :min="0"
              :max="100"
              style="width: 80px;"
              size="small"
            />
            <span>%</span>
          </div>
        </a-card>
      </a-col>

      <!-- 工程总造价 -->
      <a-col :span="16">
        <a-card title="工程总造价" size="small" style="border: 2px solid #f5222d;">
          <a-statistic
            :value="costSummary.total_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#f5222d', fontSize: '24px', fontWeight: 'bold' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 费用构成分析 -->
    <a-card title="费用构成分析" style="margin-top: 24px;">
      <a-row :gutter="16">
        <a-col :span="6">
          <div class="breakdown-item">
            <div class="breakdown-color" style="background-color: #52c41a;"></div>
            <span>分部分项: {{ billRatio.toFixed(1) }}%</span>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="breakdown-item">
            <div class="breakdown-color" style="background-color: #1890ff;"></div>
            <span>措施项目: {{ measureRatio.toFixed(1) }}%</span>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="breakdown-item">
            <div class="breakdown-color" style="background-color: #722ed1;"></div>
            <span>其他项目: {{ otherRatio.toFixed(1) }}%</span>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="breakdown-item">
            <div class="breakdown-color" style="background-color: #fa8c16;"></div>
            <span>税金: {{ taxRatio.toFixed(1) }}%</span>
          </div>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  unitProject: { type: Object, required: true }
})

const emit = defineEmits(['update'])

const taxRate = ref(9.0)

const costSummary = computed(() => {
  return props.unitProject?.cost_summary || {
    bill_cost: 0,
    measure_cost: 0,
    other_cost: 0,
    tax: 0,
    total_cost: 0
  }
})

const subtotal = computed(() => {
  return costSummary.value.bill_cost + costSummary.value.measure_cost + costSummary.value.other_cost
})

const billRatio = computed(() => {
  if (costSummary.value.total_cost === 0) return 0
  return (costSummary.value.bill_cost / costSummary.value.total_cost) * 100
})

const measureRatio = computed(() => {
  if (costSummary.value.total_cost === 0) return 0
  return (costSummary.value.measure_cost / costSummary.value.total_cost) * 100
})

const otherRatio = computed(() => {
  if (costSummary.value.total_cost === 0) return 0
  return (costSummary.value.other_cost / costSummary.value.total_cost) * 100
})

const taxRatio = computed(() => {
  if (costSummary.value.total_cost === 0) return 0
  return (costSummary.value.tax / costSummary.value.total_cost) * 100
})

const onTaxRateChange = () => {
  updateCostSummary()
}

const updateCostSummary = () => {
  const updatedProject = { ...props.unitProject }
  
  if (!updatedProject.cost_summary) {
    updatedProject.cost_summary = {
      bill_cost: 0,
      measure_cost: 0,
      other_cost: 0,
      tax: 0,
      total_cost: 0
    }
  }
  
  const tax = subtotal.value * (taxRate.value / 100)
  
  updatedProject.cost_summary.tax = tax
  updatedProject.cost_summary.total_cost = subtotal.value + tax
  
  emit('update', updatedProject)
}

watch(() => props.unitProject, () => {
  // 自动计算费用汇总
  updateCostSummary()
}, { immediate: true, deep: true })
</script>

<style scoped>
.cost-summary {
  padding: 24px;
}

.breakdown-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.breakdown-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

:deep(.ant-card-head-title) {
  text-align: center;
  font-weight: 600;
}
</style>
