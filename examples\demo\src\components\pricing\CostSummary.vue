<template>
  <div class="cost-summary">
    <a-row :gutter="[16, 16]">
      <!-- 分部分项工程费 -->
      <a-col :span="8">
        <a-card title="分部分项工程费" size="small" class="summary-card">
          <a-statistic
            :value="costSummary.bill_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#52c41a', fontSize: '20px' }"
          />
          <div class="summary-detail">
            <p>清单项数量: {{ billItemsCount }}</p>
            <p>平均单价: {{ averageBillPrice.toFixed(2) }} 元</p>
          </div>
        </a-card>
      </a-col>

      <!-- 措施项目费 -->
      <a-col :span="8">
        <a-card title="措施项目费" size="small" class="summary-card">
          <a-statistic
            :value="costSummary.measure_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#1890ff', fontSize: '20px' }"
          />
          <div class="summary-detail">
            <p>措施项数量: {{ measureItemsCount }}</p>
            <p>占分部分项比例: {{ measureRatio.toFixed(2) }}%</p>
          </div>
        </a-card>
      </a-col>

      <!-- 其他项目费 -->
      <a-col :span="8">
        <a-card title="其他项目费" size="small" class="summary-card">
          <a-statistic
            :value="costSummary.other_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#722ed1', fontSize: '20px' }"
          />
          <div class="summary-detail">
            <p>其他项数量: {{ otherItemsCount }}</p>
            <p>占工程费比例: {{ otherRatio.toFixed(2) }}%</p>
          </div>
        </a-card>
      </a-col>

      <!-- 规费 -->
      <a-col :span="8">
        <a-card title="规费" size="small" class="summary-card">
          <div class="fee-input-section">
            <a-input-number
              v-model:value="editableFees.regulatory_fee"
              @change="onFeeChange"
              :precision="2"
              :min="0"
              style="width: 100%;"
              placeholder="请输入规费"
            />
          </div>
          <div class="summary-detail">
            <p>费率设置: 
              <a-input-number
                v-model:value="editableFees.regulatory_rate"
                @change="onRateChange('regulatory')"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 80px;"
                size="small"
              />%
            </p>
          </div>
        </a-card>
      </a-col>

      <!-- 税金 -->
      <a-col :span="8">
        <a-card title="税金" size="small" class="summary-card">
          <a-statistic
            :value="costSummary.tax"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#fa8c16', fontSize: '20px' }"
          />
          <div class="summary-detail">
            <p>税率: 
              <a-input-number
                v-model:value="editableFees.tax_rate"
                @change="onRateChange('tax')"
                :precision="2"
                :min="0"
                :max="100"
                style="width: 80px;"
                size="small"
              />%
            </p>
            <p>计税方式: {{ taxMethodText }}</p>
          </div>
        </a-card>
      </a-col>

      <!-- 工程总造价 -->
      <a-col :span="8">
        <a-card title="工程总造价" size="small" class="total-card">
          <a-statistic
            :value="costSummary.total_cost"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#f5222d', fontSize: '24px', fontWeight: 'bold' }"
          />
          <div class="summary-detail">
            <p>含税总价</p>
            <p>大写: {{ totalCostInChinese }}</p>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 详细费用构成图表 -->
    <a-card title="费用构成分析" style="margin-top: 24px;">
      <div class="cost-breakdown">
        <a-progress
          type="dashboard"
          :percent="100"
          :format="() => '费用构成'"
          :stroke-color="{
            '0%': '#108ee9',
            '100%': '#87d068',
          }"
          style="margin-bottom: 24px;"
        />
        
        <a-row :gutter="16">
          <a-col :span="6">
            <div class="breakdown-item">
              <div class="breakdown-color" style="background-color: #52c41a;"></div>
              <span>分部分项: {{ billRatio.toFixed(1) }}%</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="breakdown-item">
              <div class="breakdown-color" style="background-color: #1890ff;"></div>
              <span>措施项目: {{ measureRatio.toFixed(1) }}%</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="breakdown-item">
              <div class="breakdown-color" style="background-color: #722ed1;"></div>
              <span>其他项目: {{ otherRatio.toFixed(1) }}%</span>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="breakdown-item">
              <div class="breakdown-color" style="background-color: #fa8c16;"></div>
              <span>税金: {{ taxRatio.toFixed(1) }}%</span>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'

// Props
const props = defineProps({
  unitProject: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update'])

// 响应式数据
const editableFees = reactive({
  regulatory_fee: 0,
  regulatory_rate: 3.5,
  tax_rate: 9.0
})

// 计算属性
const costSummary = computed(() => {
  return props.unitProject?.cost_summary || {
    bill_cost: 0,
    measure_cost: 0,
    other_cost: 0,
    tax: 0,
    total_cost: 0
  }
})

const billItemsCount = computed(() => {
  return props.unitProject?.bill_items?.length || 0
})

const measureItemsCount = computed(() => {
  return props.unitProject?.measure_items?.length || 0
})

const otherItemsCount = computed(() => {
  return props.unitProject?.other_items?.length || 0
})

const averageBillPrice = computed(() => {
  if (billItemsCount.value === 0) return 0
  return costSummary.value.bill_cost / billItemsCount.value
})

const subtotal = computed(() => {
  return costSummary.value.bill_cost + costSummary.value.measure_cost + costSummary.value.other_cost
})

const billRatio = computed(() => {
  if (costSummary.value.total_cost === 0) return 0
  return (costSummary.value.bill_cost / costSummary.value.total_cost) * 100
})

const measureRatio = computed(() => {
  if (costSummary.value.bill_cost === 0) return 0
  return (costSummary.value.measure_cost / costSummary.value.bill_cost) * 100
})

const otherRatio = computed(() => {
  if (subtotal.value === 0) return 0
  return (costSummary.value.other_cost / subtotal.value) * 100
})

const taxRatio = computed(() => {
  if (costSummary.value.total_cost === 0) return 0
  return (costSummary.value.tax / costSummary.value.total_cost) * 100
})

const taxMethodText = computed(() => {
  // 这里需要从项目信息中获取计税方式
  return '一般计税' // 暂时硬编码
})

const totalCostInChinese = computed(() => {
  // 简化的数字转中文大写
  const num = Math.round(costSummary.value.total_cost)
  if (num === 0) return '零元整'
  return `${num.toLocaleString()}元整` // 简化版本
})

// 费用变化处理
const onFeeChange = () => {
  updateCostSummary()
}

// 费率变化处理
const onRateChange = (type) => {
  if (type === 'regulatory') {
    // 根据费率计算规费
    editableFees.regulatory_fee = subtotal.value * (editableFees.regulatory_rate / 100)
  } else if (type === 'tax') {
    // 根据税率计算税金
    updateCostSummary()
  }
  updateCostSummary()
}

// 更新费用汇总
const updateCostSummary = () => {
  const updatedProject = { ...props.unitProject }
  
  if (!updatedProject.cost_summary) {
    updatedProject.cost_summary = {
      bill_cost: 0,
      measure_cost: 0,
      other_cost: 0,
      tax: 0,
      total_cost: 0
    }
  }
  
  // 计算税金
  const taxableAmount = subtotal.value + editableFees.regulatory_fee
  const tax = taxableAmount * (editableFees.tax_rate / 100)
  
  updatedProject.cost_summary.tax = tax
  updatedProject.cost_summary.total_cost = taxableAmount + tax
  
  emit('update', updatedProject)
}

// 监听单位工程变化
watch(() => props.unitProject, () => {
  // 初始化费率
  if (props.unitProject?.cost_summary) {
    editableFees.regulatory_fee = 0 // 规费需要单独计算
    // 可以从项目配置中读取默认费率
  }
}, { immediate: true, deep: true })
</script>

<style scoped>
.cost-summary {
  padding: 24px;
}

.summary-card {
  height: 180px;
}

.total-card {
  height: 180px;
  border: 2px solid #f5222d;
}

.summary-detail {
  margin-top: 12px;
  font-size: 12px;
  color: #666;
}

.summary-detail p {
  margin: 4px 0;
}

.fee-input-section {
  margin-bottom: 12px;
}

.cost-breakdown {
  text-align: center;
}

.breakdown-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.breakdown-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

:deep(.ant-statistic-content) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-card-head-title) {
  text-align: center;
  font-weight: 600;
}

:deep(.ant-card-body) {
  padding: 16px;
}
</style>
