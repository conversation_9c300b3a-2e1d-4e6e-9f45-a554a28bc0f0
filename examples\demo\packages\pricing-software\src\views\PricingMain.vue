<template>
  <div class="pricing-main">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-left">
        <h2>计价软件</h2>
        <span class="version">v0.0.6</span>
      </div>
      <div class="nav-right">
        <a-button type="primary" @click="openConsole">
          <template #icon>
            <AppstoreOutlined />
          </template>
          项目控制台
        </a-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 功能卡片网格 -->
      <div class="feature-grid">
        <a-card 
          class="feature-card"
          hoverable
          @click="openConsole"
        >
          <template #cover>
            <div class="card-icon">
              <FolderOpenOutlined />
            </div>
          </template>
          <a-card-meta
            title="项目管理"
            description="创建、打开、管理计价项目"
          />
        </a-card>

        <a-card 
          class="feature-card"
          hoverable
          @click="importXmlProject"
        >
          <template #cover>
            <div class="card-icon">
              <ImportOutlined />
            </div>
          </template>
          <a-card-meta
            title="XML导入"
            description="导入招标文件和清单数据"
          />
        </a-card>

        <a-card 
          class="feature-card"
          hoverable
          @click="openTemplateLibrary"
        >
          <template #cover>
            <div class="card-icon">
              <BookOutlined />
            </div>
          </template>
          <a-card-meta
            title="模板库"
            description="管理项目模板和标准清单"
          />
        </a-card>

        <a-card 
          class="feature-card"
          hoverable
          @click="openSettings"
        >
          <template #cover>
            <div class="card-icon">
              <SettingOutlined />
            </div>
          </template>
          <a-card-meta
            title="系统设置"
            description="配置软件参数和用户偏好"
          />
        </a-card>
      </div>

      <!-- 最近项目 -->
      <div class="recent-section">
        <h3>最近项目</h3>
        <div class="recent-list" v-if="recentProjects.length > 0">
          <a-card 
            v-for="project in recentProjects" 
            :key="project.path"
            class="recent-card"
            size="small"
            hoverable
            @click="openRecentProject(project)"
          >
            <div class="recent-info">
              <div class="project-name">{{ project.project_name }}</div>
              <div class="project-path">{{ project.path }}</div>
              <div class="last-opened">{{ formatDate(project.last_opened) }}</div>
            </div>
          </a-card>
        </div>
        <a-empty v-else description="暂无最近项目" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/tauri'
import { 
  AppstoreOutlined,
  FolderOpenOutlined, 
  ImportOutlined,
  BookOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const recentProjects = ref([])

// 打开项目控制台
const openConsole = () => {
  router.push('/console')
}

// 导入XML项目
const importXmlProject = async () => {
  try {
    const projectData = await invoke('import_xml_project')
    message.success('XML 项目导入成功')
    // 跳转到工作台
    router.push('/workbench')
  } catch (error) {
    if (error !== '用户取消了文件选择') {
      message.error(`导入失败: ${error}`)
    }
  }
}

// 打开模板库
const openTemplateLibrary = () => {
  message.info('模板库功能开发中...')
}

// 打开设置
const openSettings = () => {
  message.info('系统设置功能开发中...')
}

// 打开最近项目
const openRecentProject = async (project) => {
  try {
    await invoke('open_project_file', { filePath: project.path })
    message.success('项目打开成功')
    router.push('/workbench')
  } catch (error) {
    message.error(`打开失败: ${error}`)
    // 刷新最近项目列表
    loadRecentProjects()
  }
}

// 加载最近项目
const loadRecentProjects = async () => {
  try {
    const projects = await invoke('get_recent_projects')
    recentProjects.value = projects || []
  } catch (error) {
    console.error('加载最近项目失败:', error)
  }
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  loadRecentProjects()
})
</script>

<style scoped>
.pricing-main {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-left h2 {
  margin: 0;
  color: #1890ff;
}

.version {
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.feature-card {
  text-align: center;
  transition: all 0.3s;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  font-size: 32px;
  color: #1890ff;
  background: #f0f8ff;
}

.recent-section h3 {
  margin-bottom: 16px;
  color: #333;
}

.recent-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.recent-card {
  cursor: pointer;
  transition: all 0.3s;
}

.recent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recent-info {
  padding: 8px;
}

.project-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.project-path {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  word-break: break-all;
}

.last-opened {
  font-size: 12px;
  color: #999;
}
</style>
