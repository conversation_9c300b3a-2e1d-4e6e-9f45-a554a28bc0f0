/// Tauri 命令模块
/// 
/// 实现前端调用的 Tauri 命令，包括文件操作、项目管理等功能

use super::models::PricingProject;
use super::file_io::{Ysf<PERSON>ile<PERSON><PERSON><PERSON>, <PERSON>fig<PERSON>anager, RecentFilesManager, RecentFile, FileDialogHelper};
use super::xml_import::XmlImporter;
use tauri::{command, AppHand<PERSON>, Manager};
use tauri_plugin_dialog::{DialogExt, MessageDialogKind};
use std::path::PathBuf;
use std::sync::Mutex;
use anyhow::Result;

/// 全局状态：当前项目
pub type CurrentProject = Mutex<Option<PricingProject>>;

/// 全局状态：当前项目文件路径
pub type CurrentProjectPath = Mutex<Option<PathBuf>>;

/// 全局状态：最近文件管理器
pub type RecentFilesState = Mutex<RecentFilesManager>;

/// 创建新项目
#[command]
pub async fn create_new_project(
    app_handle: AppHandle,
    project_info: serde_json::Value,
) -> Result<String, String> {
    // 解析项目基本信息
    let project_name = project_info.get("name")
        .and_then(|v| v.as_str())
        .unwrap_or("新建项目")
        .to_string();
    
    let project_code = project_info.get("code")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();
    
    let list_standard = project_info.get("listStandard")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();
    
    let quota_standard = project_info.get("quotaStandard")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();
    
    // 打开文件保存对话框
    let (tx, rx) = tokio::sync::oneshot::channel();
    app_handle.dialog()
        .file()
        .add_filter(FileDialogHelper::get_ysf_filter().0, FileDialogHelper::get_ysf_filter().1)
        .set_file_name(&format!("{}.ysf", project_name))
        .save_file(move |file_path| {
            let _ = tx.send(file_path);
        });
    let file_path = rx.await.map_err(|_| "文件对话框错误")?;
    
    match file_path {
        Some(path) => {
            // 创建新项目
            let mut project = PricingProject::default();
            project.info.name = project_name.clone();
            project.info.code = project_code;
            project.info.list_standard = list_standard;
            project.info.quota_standard = quota_standard;
            
            // 保存项目文件
            match YsfFileHandler::save_project(&project, path.as_path().unwrap()) {
                Ok(_) => {
                    // 更新全局状态
                    if let Some(current_project) = app_handle.try_state::<CurrentProject>() {
                        *current_project.lock().unwrap() = Some(project);
                    }
                    
                    if let Some(current_path) = app_handle.try_state::<CurrentProjectPath>() {
                        *current_path.lock().unwrap() = Some(path.as_path().unwrap().to_path_buf());
                    }
                    
                    // 添加到最近文件
                    if let Some(recent_files) = app_handle.try_state::<RecentFilesState>() {
                        let mut manager = recent_files.lock().unwrap();
                        manager.add_recent_file(path.as_path().unwrap().to_path_buf(), project_name);
                        let _ = ConfigManager::save_recent_files(&manager);
                    }
                    
                    Ok(path.to_string())
                }
                Err(e) => Err(format!("保存项目失败: {}", e)),
            }
        }
        None => Err("用户取消了文件保存".to_string()),
    }
}

/// 打开项目文件
#[command]
pub async fn open_project_file(
    app_handle: AppHandle,
    file_path: Option<String>,
) -> Result<serde_json::Value, String> {
    let path = if let Some(path_str) = file_path {
        PathBuf::from(path_str)
    } else {
        // 打开文件选择对话框
        let (tx, rx) = tokio::sync::oneshot::channel();
        app_handle.dialog()
            .file()
            .add_filter(FileDialogHelper::get_ysf_filter().0, FileDialogHelper::get_ysf_filter().1)
            .pick_file(move |file_path| {
                let _ = tx.send(file_path);
            });
        match rx.await.map_err(|_| "文件对话框错误")? {
            Some(path) => path.as_path().unwrap_or(&PathBuf::new()).to_path_buf(),
            None => return Err("用户取消了文件选择".to_string()),
        }
    };
    
    // 验证文件格式
    match YsfFileHandler::validate_ysf_file(&path) {
        Ok(true) => {
            // 加载项目
            match YsfFileHandler::load_project(&path) {
                Ok(project) => {
                    // 更新全局状态
                    if let Some(current_project) = app_handle.try_state::<CurrentProject>() {
                        *current_project.lock().unwrap() = Some(project.clone());
                    }
                    
                    if let Some(current_path) = app_handle.try_state::<CurrentProjectPath>() {
                        *current_path.lock().unwrap() = Some(path.clone());
                    }
                    
                    // 添加到最近文件
                    if let Some(recent_files) = app_handle.try_state::<RecentFilesState>() {
                        let mut manager = recent_files.lock().unwrap();
                        manager.add_recent_file(path.clone(), project.info.name.clone());
                        let _ = ConfigManager::save_recent_files(&manager);
                    }
                    
                    // 返回项目数据
                    match serde_json::to_value(&project) {
                        Ok(value) => Ok(value),
                        Err(e) => Err(format!("序列化项目数据失败: {}", e)),
                    }
                }
                Err(e) => Err(format!("加载项目失败: {}", e)),
            }
        }
        Ok(false) => Err("无效的项目文件格式".to_string()),
        Err(e) => Err(format!("验证文件失败: {}", e)),
    }
}

/// 保存当前项目
#[command]
pub async fn save_current_project(
    app_handle: AppHandle,
    project_data: serde_json::Value,
) -> Result<String, String> {
    // 获取当前项目路径
    let current_path = if let Some(path_state) = app_handle.try_state::<CurrentProjectPath>() {
        path_state.lock().unwrap().clone()
    } else {
        None
    };
    
    match current_path {
        Some(path) => {
            // 反序列化项目数据
            match serde_json::from_value::<PricingProject>(project_data) {
                Ok(mut project) => {
                    // 更新修改时间
                    project.updated_at = chrono::Utc::now();
                    
                    // 保存项目
                    match YsfFileHandler::save_project(&project, &path) {
                        Ok(_) => {
                            // 更新全局状态
                            if let Some(current_project) = app_handle.try_state::<CurrentProject>() {
                                *current_project.lock().unwrap() = Some(project);
                            }
                            
                            Ok("项目保存成功".to_string())
                        }
                        Err(e) => Err(format!("保存项目失败: {}", e)),
                    }
                }
                Err(e) => Err(format!("解析项目数据失败: {}", e)),
            }
        }
        None => Err("没有当前项目文件路径".to_string()),
    }
}

/// 另存为项目
#[command]
pub async fn save_project_as(
    app_handle: AppHandle,
    project_data: serde_json::Value,
) -> Result<String, String> {
    // 反序列化项目数据
    let project = match serde_json::from_value::<PricingProject>(project_data) {
        Ok(project) => project,
        Err(e) => return Err(format!("解析项目数据失败: {}", e)),
    };
    
    // 打开文件保存对话框
    let (tx, rx) = tokio::sync::oneshot::channel();
    app_handle.dialog()
        .file()
        .add_filter(FileDialogHelper::get_ysf_filter().0, FileDialogHelper::get_ysf_filter().1)
        .set_file_name(&format!("{}.ysf", project.info.name))
        .save_file(move |file_path| {
            let _ = tx.send(file_path);
        });
    let file_path = rx.await.map_err(|_| "文件对话框错误")?;
    
    match file_path {
        Some(path) => {
            // 保存项目
            match YsfFileHandler::save_project(&project, path.as_path().unwrap()) {
                Ok(_) => {
                    // 更新全局状态
                    if let Some(current_project) = app_handle.try_state::<CurrentProject>() {
                        *current_project.lock().unwrap() = Some(project.clone());
                    }
                    
                    if let Some(current_path) = app_handle.try_state::<CurrentProjectPath>() {
                        *current_path.lock().unwrap() = Some(path.as_path().unwrap().to_path_buf());
                    }
                    
                    // 添加到最近文件
                    if let Some(recent_files) = app_handle.try_state::<RecentFilesState>() {
                        let mut manager = recent_files.lock().unwrap();
                        manager.add_recent_file(path.as_path().unwrap().to_path_buf(), project.info.name.clone());
                        let _ = ConfigManager::save_recent_files(&manager);
                    }
                    
                    Ok(path.to_string())
                }
                Err(e) => Err(format!("保存项目失败: {}", e)),
            }
        }
        None => Err("用户取消了文件保存".to_string()),
    }
}

/// 获取最近打开的文件列表
#[command]
pub async fn get_recent_projects(app_handle: AppHandle) -> Result<Vec<RecentFile>, String> {
    if let Some(recent_files) = app_handle.try_state::<RecentFilesState>() {
        let manager = recent_files.lock().unwrap();
        Ok(manager.get_recent_files().clone())
    } else {
        // 尝试从配置文件加载
        match ConfigManager::load_recent_files() {
            Ok(manager) => {
                let files = manager.get_recent_files().clone();
                
                // 更新全局状态
                if let Some(recent_files) = app_handle.try_state::<RecentFilesState>() {
                    *recent_files.lock().unwrap() = manager;
                }
                
                Ok(files)
            }
            Err(e) => Err(format!("加载最近文件失败: {}", e)),
        }
    }
}

/// 获取当前项目信息
#[command]
pub async fn get_current_project(app_handle: AppHandle) -> Result<Option<serde_json::Value>, String> {
    if let Some(current_project) = app_handle.try_state::<CurrentProject>() {
        let project = current_project.lock().unwrap().clone();
        match project {
            Some(p) => match serde_json::to_value(&p) {
                Ok(value) => Ok(Some(value)),
                Err(e) => Err(format!("序列化项目数据失败: {}", e)),
            },
            None => Ok(None),
        }
    } else {
        Ok(None)
    }
}

/// 导入 XML 项目文件
#[command]
pub async fn import_xml_project(
    app_handle: AppHandle,
    file_path: Option<String>,
) -> Result<serde_json::Value, String> {
    let path = if let Some(path_str) = file_path {
        PathBuf::from(path_str)
    } else {
        // 打开文件选择对话框
        let (tx, rx) = tokio::sync::oneshot::channel();
        app_handle.dialog()
            .file()
            .add_filter(FileDialogHelper::get_xml_filter().0, FileDialogHelper::get_xml_filter().1)
            .pick_file(move |file_path| {
                let _ = tx.send(file_path);
            });
        match rx.await.map_err(|_| "文件对话框错误")? {
            Some(path) => path.as_path().unwrap_or(&PathBuf::new()).to_path_buf(),
            None => return Err("用户取消了文件选择".to_string()),
        }
    };

    // 验证 XML 文件格式
    match XmlImporter::validate_xml_file(&path) {
        Ok(true) => {
            // 导入项目
            match XmlImporter::import_from_file(&path) {
                Ok(project) => {
                    // 更新全局状态（但不保存文件，让用户手动保存）
                    if let Some(current_project) = app_handle.try_state::<CurrentProject>() {
                        *current_project.lock().unwrap() = Some(project.clone());
                    }

                    // 清除当前项目路径（因为这是导入的项目，还未保存）
                    if let Some(current_path) = app_handle.try_state::<CurrentProjectPath>() {
                        *current_path.lock().unwrap() = None;
                    }

                    // 返回项目数据
                    match serde_json::to_value(&project) {
                        Ok(value) => Ok(value),
                        Err(e) => Err(format!("序列化项目数据失败: {}", e)),
                    }
                }
                Err(e) => Err(format!("导入 XML 项目失败: {}", e)),
            }
        }
        Ok(false) => Err("无效的 XML 文件格式".to_string()),
        Err(e) => Err(format!("验证 XML 文件失败: {}", e)),
    }
}
