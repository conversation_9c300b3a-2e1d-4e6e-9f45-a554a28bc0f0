import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'PricingMain',
    component: () => import('../views/PricingMain.vue'),
    meta: { title: '计价软件' }
  },
  {
    path: '/console',
    name: 'Console',
    component: () => import('../views/Console.vue'),
    meta: { title: '项目控制台' }
  },
  {
    path: '/workbench',
    name: 'Workbench',
    component: () => import('../views/Workbench.vue'),
    meta: { title: '项目工作台' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
