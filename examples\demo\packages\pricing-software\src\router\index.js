import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: '<PERSON><PERSON><PERSON>',
    component: () => import('../views/Console.vue'),
    meta: { title: '计价软件控制台' }
  },
  {
    path: '/workbench',
    name: 'Workbench',
    component: () => import('../views/Workbench.vue'),
    meta: { title: '计价软件工作台' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
