/// 计价软件数据模型定义
/// 
/// 根据 CLAUDE.md 文档中的需求，定义项目的核心数据结构

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};

/// 项目主数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PricingProject {
    /// 项目基本信息
    pub info: ProjectInfo,
    /// 单项工程列表
    pub single_projects: Vec<SingleProject>,
    /// 项目创建时间
    pub created_at: DateTime<Utc>,
    /// 最后修改时间
    pub updated_at: DateTime<Utc>,
    /// 项目版本
    pub version: String,
}

/// 项目基本信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectInfo {
    /// 项目名称
    pub name: String,
    /// 项目编码
    pub code: String,
    /// 清单标准
    pub list_standard: String,
    /// 定额标准
    pub quota_standard: String,
    /// 计税方式 (一般计税/简易计税)
    pub tax_method: TaxMethod,
    /// 工程概况
    pub overview: ProjectOverview,
}

/// 计税方式枚举
#[derive(Debu<PERSON>, Clone, Serialize, Deserialize)]
pub enum TaxMethod {
    /// 一般计税
    General,
    /// 简易计税
    Simple,
}

impl Default for TaxMethod {
    fn default() -> Self {
        TaxMethod::General
    }
}

/// 工程概况
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectOverview {
    /// 编制说明
    pub description: String,
    /// 工程特征
    pub characteristics: String,
    /// 其他信息
    pub other_info: HashMap<String, String>,
}

/// 单项工程
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SingleProject {
    /// 单项工程ID
    pub id: String,
    /// 单项工程名称
    pub name: String,
    /// 单位工程列表
    pub unit_projects: Vec<UnitProject>,
}

/// 单位工程
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnitProject {
    /// 单位工程ID
    pub id: String,
    /// 单位工程名称
    pub name: String,
    /// 分部分项工程
    pub bill_items: Vec<BillItem>,
    /// 措施项目
    pub measure_items: Vec<MeasureItem>,
    /// 其他项目
    pub other_items: Vec<OtherItem>,
    /// 费用汇总
    pub cost_summary: CostSummary,
}

/// 分部分项工程清单项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BillItem {
    /// 清单序号
    pub sequence: String,
    /// 项目编码
    pub code: String,
    /// 项目名称
    pub name: String,
    /// 项目特征
    pub characteristics: String,
    /// 计量单位
    pub unit: String,
    /// 工程量
    pub quantity: f64,
    /// 综合单价
    pub unit_price: f64,
    /// 合价
    pub total_price: f64,
    /// 关联的定额项
    pub quota_items: Vec<QuotaItem>,
}

/// 定额项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuotaItem {
    /// 定额编码
    pub code: String,
    /// 定额名称
    pub name: String,
    /// 计量单位
    pub unit: String,
    /// 工程量
    pub quantity: f64,
    /// 人工费
    pub labor_cost: f64,
    /// 材料费
    pub material_cost: f64,
    /// 机械费
    pub machinery_cost: f64,
    /// 管理费
    pub management_cost: f64,
    /// 利润
    pub profit: f64,
}

/// 措施项目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeasureItem {
    /// 项目编码
    pub code: String,
    /// 项目名称
    pub name: String,
    /// 计算基础
    pub calculation_base: String,
    /// 费率(%)
    pub rate: f64,
    /// 金额
    pub amount: f64,
}

/// 其他项目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OtherItem {
    /// 项目名称
    pub name: String,
    /// 项目类型 (暂列金额、专业工程暂估价、计日工等)
    pub item_type: OtherItemType,
    /// 金额
    pub amount: f64,
    /// 备注
    pub remark: String,
}

/// 其他项目类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OtherItemType {
    /// 暂列金额
    ProvisionalSum,
    /// 专业工程暂估价
    ProfessionalEstimate,
    /// 计日工
    DayWork,
    /// 总承包服务费
    GeneralContractorFee,
}

/// 费用汇总
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CostSummary {
    /// 分部分项工程费
    pub bill_cost: f64,
    /// 措施项目费
    pub measure_cost: f64,
    /// 其他项目费
    pub other_cost: f64,
    /// 规费
    /// 税金
    pub tax: f64,
    /// 工程总造价
    pub total_cost: f64,
}

impl Default for PricingProject {
    fn default() -> Self {
        Self {
            info: ProjectInfo::default(),
            single_projects: vec![],
            created_at: Utc::now(),
            updated_at: Utc::now(),
            version: "0.0.6".to_string(),
        }
    }
}

impl Default for ProjectInfo {
    fn default() -> Self {
        Self {
            name: "新建项目".to_string(),
            code: "".to_string(),
            list_standard: "".to_string(),
            quota_standard: "".to_string(),
            tax_method: TaxMethod::default(),
            overview: ProjectOverview::default(),
        }
    }
}

impl Default for ProjectOverview {
    fn default() -> Self {
        Self {
            description: "".to_string(),
            characteristics: "".to_string(),
            other_info: HashMap::new(),
        }
    }
}

impl Default for CostSummary {
    fn default() -> Self {
        Self {
            bill_cost: 0.0,
            measure_cost: 0.0,
            other_cost: 0.0,
            tax: 0.0,
            total_cost: 0.0,
        }
    }
}
