<template>
  <div class="other-items-table">
    <div class="table-toolbar">
      <a-button type="primary" @click="addOtherItem">
        <template #icon>
          <PlusOutlined />
        </template>
        添加其他项目
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="otherItems"
      :pagination="false"
      row-key="name"
      size="small"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <a-input v-model:value="record.name" @blur="onCellChange" size="small" />
        </template>
        
        <template v-else-if="column.key === 'item_type'">
          <a-select v-model:value="record.item_type" @change="onCellChange" size="small" style="width: 100%;">
            <a-select-option value="ProvisionalSum">暂列金额</a-select-option>
            <a-select-option value="ProfessionalEstimate">专业工程暂估价</a-select-option>
            <a-select-option value="DayWork">计日工</a-select-option>
            <a-select-option value="GeneralContractorFee">总承包服务费</a-select-option>
          </a-select>
        </template>
        
        <template v-else-if="column.key === 'amount'">
          <a-input-number
            v-model:value="record.amount"
            @blur="onCellChange"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <template v-else-if="column.key === 'actions'">
          <a-popconfirm
            title="确定删除这个其他项目吗？"
            @confirm="deleteOtherItem(record)"
          >
            <a-button type="link" size="small" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  unitProject: { type: Object, required: true }
})

const emit = defineEmits(['update'])

const columns = [
  { title: '项目名称', key: 'name', width: 250 },
  { title: '项目类型', key: 'item_type', width: 180 },
  { title: '金额(元)', key: 'amount', width: 150, align: 'right' },
  { title: '操作', key: 'actions', width: 100 }
]

const otherItems = computed(() => props.unitProject?.other_items || [])

const addOtherItem = () => {
  const newItem = {
    name: '',
    item_type: 'ProvisionalSum',
    amount: 0,
    remark: ''
  }
  
  const updatedProject = { ...props.unitProject }
  if (!updatedProject.other_items) {
    updatedProject.other_items = []
  }
  updatedProject.other_items.push(newItem)
  
  emit('update', updatedProject)
}

const deleteOtherItem = (item) => {
  const updatedProject = { ...props.unitProject }
  const index = updatedProject.other_items.findIndex(i => i.name === item.name)
  if (index > -1) {
    updatedProject.other_items.splice(index, 1)
    emit('update', updatedProject)
    message.success('其他项目删除成功')
  }
}

const onCellChange = () => {
  emit('update', props.unitProject)
}
</script>

<style scoped>
.other-items-table {
  height: 100%;
}

.table-toolbar {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
