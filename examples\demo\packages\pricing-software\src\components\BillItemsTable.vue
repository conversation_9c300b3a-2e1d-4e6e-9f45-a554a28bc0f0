<template>
  <div class="bill-items-table">
    <div class="table-toolbar">
      <div class="toolbar-left">
        <a-button type="primary" @click="addBillItem">
          <template #icon>
            <PlusOutlined />
          </template>
          添加清单项
        </a-button>
      </div>
      
      <div class="toolbar-right">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索清单项..."
          style="width: 300px;"
        />
      </div>
    </div>

    <a-table
      :columns="columns"
      :data-source="filteredBillItems"
      :pagination="{ pageSize: 50 }"
      :scroll="{ x: 1200, y: 400 }"
      row-key="sequence"
      size="small"
      bordered
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'sequence'">
          <span>{{ index + 1 }}</span>
        </template>
        
        <template v-else-if="column.key === 'code'">
          <a-input
            v-model:value="record.code"
            @blur="onCellChange"
            size="small"
          />
        </template>
        
        <template v-else-if="column.key === 'name'">
          <a-input
            v-model:value="record.name"
            @blur="onCellChange"
            size="small"
          />
        </template>
        
        <template v-else-if="column.key === 'unit'">
          <a-select
            v-model:value="record.unit"
            @change="onCellChange"
            size="small"
            style="width: 100%;"
          >
            <a-select-option value="m">m</a-select-option>
            <a-select-option value="m²">m²</a-select-option>
            <a-select-option value="m³">m³</a-select-option>
            <a-select-option value="t">t</a-select-option>
            <a-select-option value="个">个</a-select-option>
          </a-select>
        </template>
        
        <template v-else-if="column.key === 'quantity'">
          <a-input-number
            v-model:value="record.quantity"
            @blur="onQuantityChange(record)"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <template v-else-if="column.key === 'unit_price'">
          <a-input-number
            v-model:value="record.unit_price"
            @blur="onUnitPriceChange(record)"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <template v-else-if="column.key === 'total_price'">
          <span class="price-cell">{{ formatCurrency(record.total_price) }}</span>
        </template>
        
        <template v-else-if="column.key === 'actions'">
          <a-popconfirm
            title="确定删除这个清单项吗？"
            @confirm="deleteBillItem(record)"
          >
            <a-button type="link" size="small" danger>
              删除
            </a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  unitProject: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

const searchText = ref('')

const columns = [
  { title: '行号', key: 'sequence', width: 60, fixed: 'left' },
  { title: '项目编码', key: 'code', width: 150, fixed: 'left' },
  { title: '项目名称', key: 'name', width: 200, fixed: 'left' },
  { title: '计量单位', key: 'unit', width: 100 },
  { title: '工程量', key: 'quantity', width: 120, align: 'right' },
  { title: '综合单价(元)', key: 'unit_price', width: 120, align: 'right' },
  { title: '合价(元)', key: 'total_price', width: 120, align: 'right' },
  { title: '操作', key: 'actions', width: 100, fixed: 'right' }
]

const filteredBillItems = computed(() => {
  if (!props.unitProject?.bill_items) return []
  
  let items = [...props.unitProject.bill_items]
  
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    items = items.filter(item => 
      item.name?.toLowerCase().includes(search) ||
      item.code?.toLowerCase().includes(search)
    )
  }
  
  return items
})

const addBillItem = () => {
  const newItem = {
    sequence: (props.unitProject.bill_items?.length || 0) + 1,
    code: '',
    name: '',
    characteristics: '',
    unit: '',
    quantity: 0,
    unit_price: 0,
    total_price: 0,
    quota_items: []
  }
  
  const updatedProject = { ...props.unitProject }
  if (!updatedProject.bill_items) {
    updatedProject.bill_items = []
  }
  updatedProject.bill_items.push(newItem)
  
  emitUpdate(updatedProject)
}

const deleteBillItem = (item) => {
  const updatedProject = { ...props.unitProject }
  const index = updatedProject.bill_items.findIndex(i => i.sequence === item.sequence)
  if (index > -1) {
    updatedProject.bill_items.splice(index, 1)
    emitUpdate(updatedProject)
    message.success('清单项删除成功')
  }
}

const onCellChange = () => {
  emitUpdate(props.unitProject)
}

const onQuantityChange = (record) => {
  record.total_price = record.quantity * record.unit_price
  emitUpdate(props.unitProject)
}

const onUnitPriceChange = (record) => {
  record.total_price = record.quantity * record.unit_price
  emitUpdate(props.unitProject)
}

const formatCurrency = (value) => {
  if (typeof value !== 'number') return '0.00'
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const emitUpdate = (updatedProject) => {
  emit('update', updatedProject)
}
</script>

<style scoped>
.bill-items-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.price-cell {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #1890ff;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
}
</style>
