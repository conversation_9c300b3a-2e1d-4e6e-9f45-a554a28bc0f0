<template>
  <div class="measure-items-table">
    <div class="table-toolbar">
      <a-button type="primary" @click="addMeasureItem">
        <template #icon>
          <PlusOutlined />
        </template>
        添加措施项目
      </a-button>
    </div>

    <a-table
      :columns="columns"
      :data-source="measureItems"
      :pagination="false"
      row-key="code"
      size="small"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'code'">
          <a-input v-model:value="record.code" @blur="onCellChange" size="small" />
        </template>
        
        <template v-else-if="column.key === 'name'">
          <a-input v-model:value="record.name" @blur="onCellChange" size="small" />
        </template>
        
        <template v-else-if="column.key === 'rate'">
          <a-input-number
            v-model:value="record.rate"
            @blur="onCellChange"
            :precision="2"
            :min="0"
            :max="100"
            size="small"
            style="width: 100%;"
            addon-after="%"
          />
        </template>
        
        <template v-else-if="column.key === 'amount'">
          <a-input-number
            v-model:value="record.amount"
            @blur="onCellChange"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <template v-else-if="column.key === 'actions'">
          <a-popconfirm
            title="确定删除这个措施项目吗？"
            @confirm="deleteMeasureItem(record)"
          >
            <a-button type="link" size="small" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  unitProject: { type: Object, required: true }
})

const emit = defineEmits(['update'])

const columns = [
  { title: '项目编码', key: 'code', width: 150 },
  { title: '项目名称', key: 'name', width: 300 },
  { title: '费率(%)', key: 'rate', width: 120, align: 'right' },
  { title: '金额(元)', key: 'amount', width: 150, align: 'right' },
  { title: '操作', key: 'actions', width: 100 }
]

const measureItems = computed(() => props.unitProject?.measure_items || [])

const addMeasureItem = () => {
  const newItem = {
    code: '',
    name: '',
    calculation_base: '',
    rate: 0,
    amount: 0
  }
  
  const updatedProject = { ...props.unitProject }
  if (!updatedProject.measure_items) {
    updatedProject.measure_items = []
  }
  updatedProject.measure_items.push(newItem)
  
  emit('update', updatedProject)
}

const deleteMeasureItem = (item) => {
  const updatedProject = { ...props.unitProject }
  const index = updatedProject.measure_items.findIndex(i => i.code === item.code)
  if (index > -1) {
    updatedProject.measure_items.splice(index, 1)
    emit('update', updatedProject)
    message.success('措施项目删除成功')
  }
}

const onCellChange = () => {
  emit('update', props.unitProject)
}
</script>

<style scoped>
.measure-items-table {
  height: 100%;
}

.table-toolbar {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
