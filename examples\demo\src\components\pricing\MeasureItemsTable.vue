<template>
  <div class="measure-items-table">
    <div class="table-toolbar">
      <div class="toolbar-left">
        <a-button type="primary" @click="addMeasureItem">
          <template #icon>
            <PlusOutlined />
          </template>
          添加措施项目
        </a-button>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data-source="measureItems"
      :pagination="false"
      :scroll="{ x: 1200, y: 600 }"
      row-key="code"
      size="small"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <!-- 项目编码列 -->
        <template v-if="column.key === 'code'">
          <a-input
            v-model:value="record.code"
            @blur="onCellChange"
            size="small"
          />
        </template>
        
        <!-- 项目名称列 -->
        <template v-else-if="column.key === 'name'">
          <a-input
            v-model:value="record.name"
            @blur="onCellChange"
            size="small"
          />
        </template>
        
        <!-- 计算基础列 -->
        <template v-else-if="column.key === 'calculation_base'">
          <a-input
            v-model:value="record.calculation_base"
            @blur="onCellChange"
            size="small"
          />
        </template>
        
        <!-- 费率列 -->
        <template v-else-if="column.key === 'rate'">
          <a-input-number
            v-model:value="record.rate"
            @blur="onRateChange(record)"
            :precision="2"
            :min="0"
            :max="100"
            size="small"
            style="width: 100%;"
            addon-after="%"
          />
        </template>
        
        <!-- 金额列 -->
        <template v-else-if="column.key === 'amount'">
          <a-input-number
            v-model:value="record.amount"
            @blur="onAmountChange(record)"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.key === 'actions'">
          <a-popconfirm
            title="确定删除这个措施项目吗？"
            @confirm="deleteMeasureItem(record)"
          >
            <a-button type="link" size="small" danger>
              删除
            </a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>

    <!-- 汇总信息 -->
    <div class="summary-section">
      <a-card title="措施项目费汇总" size="small">
        <a-statistic
          title="措施项目费总计"
          :value="totalMeasureCost"
          :precision="2"
          suffix="元"
          :value-style="{ color: '#1890ff', fontSize: '18px' }"
        />
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  unitProject: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update'])

// 表格列定义
const columns = [
  {
    title: '项目编码',
    key: 'code',
    width: 150
  },
  {
    title: '项目名称',
    key: 'name',
    width: 300
  },
  {
    title: '计算基础',
    key: 'calculation_base',
    width: 200
  },
  {
    title: '费率(%)',
    key: 'rate',
    width: 120,
    align: 'right'
  },
  {
    title: '金额(元)',
    key: 'amount',
    width: 150,
    align: 'right'
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right'
  }
]

// 计算属性
const measureItems = computed(() => {
  return props.unitProject?.measure_items || []
})

const totalMeasureCost = computed(() => {
  return measureItems.value.reduce((sum, item) => sum + (item.amount || 0), 0)
})

// 添加措施项目
const addMeasureItem = () => {
  const newItem = {
    code: '',
    name: '',
    calculation_base: '',
    rate: 0,
    amount: 0
  }
  
  const updatedProject = { ...props.unitProject }
  if (!updatedProject.measure_items) {
    updatedProject.measure_items = []
  }
  updatedProject.measure_items.push(newItem)
  
  emitUpdate(updatedProject)
}

// 删除措施项目
const deleteMeasureItem = (item) => {
  const updatedProject = { ...props.unitProject }
  const index = updatedProject.measure_items.findIndex(i => i.code === item.code)
  if (index > -1) {
    updatedProject.measure_items.splice(index, 1)
    emitUpdate(updatedProject)
    message.success('措施项目删除成功')
  }
}

// 单元格变化处理
const onCellChange = () => {
  emitUpdate(props.unitProject)
}

// 费率变化处理
const onRateChange = (record) => {
  // 这里可以根据费率自动计算金额
  // 需要知道计算基础的具体数值
  emitUpdate(props.unitProject)
}

// 金额变化处理
const onAmountChange = (record) => {
  emitUpdate(props.unitProject)
}

// 发送更新事件
const emitUpdate = (updatedProject) => {
  // 重新计算费用汇总
  const measureCost = updatedProject.measure_items?.reduce((sum, item) => sum + (item.amount || 0), 0) || 0
  
  if (!updatedProject.cost_summary) {
    updatedProject.cost_summary = {
      bill_cost: 0,
      measure_cost: 0,
      other_cost: 0,
      tax: 0,
      total_cost: 0
    }
  }
  
  updatedProject.cost_summary.measure_cost = measureCost
  updatedProject.cost_summary.total_cost = 
    updatedProject.cost_summary.bill_cost + 
    updatedProject.cost_summary.measure_cost + 
    updatedProject.cost_summary.other_cost + 
    updatedProject.cost_summary.tax
  
  emit('update', updatedProject)
}
</script>

<style scoped>
.measure-items-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.summary-section {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
}

:deep(.ant-input-number) {
  border: none;
  box-shadow: none;
}

:deep(.ant-input-number:hover) {
  border-color: #1890ff;
}

:deep(.ant-input-number:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>
