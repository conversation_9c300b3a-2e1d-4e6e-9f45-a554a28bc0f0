<template>
  <div class="bill-items-table">
    <div class="table-toolbar">
      <div class="toolbar-left">
        <a-button type="primary" @click="addBillItem">
          <template #icon>
            <PlusOutlined />
          </template>
          添加清单项
        </a-button>
        <a-button @click="importBillItems">
          <template #icon>
            <ImportOutlined />
          </template>
          导入清单
        </a-button>
      </div>
      
      <div class="toolbar-right">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索清单项..."
          style="width: 300px;"
          @search="onSearch"
        />
      </div>
    </div>

    <a-table
      :columns="columns"
      :data-source="filteredBillItems"
      :pagination="pagination"
      :scroll="{ x: 1500, y: 600 }"
      row-key="sequence"
      size="small"
      bordered
    >
      <!-- 序号列 -->
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'sequence'">
          <span>{{ index + 1 }}</span>
        </template>
        
        <!-- 项目编码列 -->
        <template v-else-if="column.key === 'code'">
          <a-input
            v-model:value="record.code"
            @blur="onCellChange(record)"
            size="small"
          />
        </template>
        
        <!-- 项目名称列 -->
        <template v-else-if="column.key === 'name'">
          <a-input
            v-model:value="record.name"
            @blur="onCellChange(record)"
            size="small"
          />
        </template>
        
        <!-- 项目特征列 -->
        <template v-else-if="column.key === 'characteristics'">
          <a-textarea
            v-model:value="record.characteristics"
            @blur="onCellChange(record)"
            :rows="2"
            size="small"
          />
        </template>
        
        <!-- 计量单位列 -->
        <template v-else-if="column.key === 'unit'">
          <a-select
            v-model:value="record.unit"
            @change="onCellChange(record)"
            size="small"
            style="width: 100%;"
          >
            <a-select-option value="m">m</a-select-option>
            <a-select-option value="m²">m²</a-select-option>
            <a-select-option value="m³">m³</a-select-option>
            <a-select-option value="t">t</a-select-option>
            <a-select-option value="kg">kg</a-select-option>
            <a-select-option value="个">个</a-select-option>
            <a-select-option value="项">项</a-select-option>
          </a-select>
        </template>
        
        <!-- 工程量列 -->
        <template v-else-if="column.key === 'quantity'">
          <a-input-number
            v-model:value="record.quantity"
            @blur="onQuantityChange(record)"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <!-- 综合单价列 -->
        <template v-else-if="column.key === 'unit_price'">
          <a-input-number
            v-model:value="record.unit_price"
            @blur="onUnitPriceChange(record)"
            :precision="2"
            :min="0"
            size="small"
            style="width: 100%;"
          />
        </template>
        
        <!-- 合价列 -->
        <template v-else-if="column.key === 'total_price'">
          <span class="price-cell">{{ formatCurrency(record.total_price) }}</span>
        </template>
        
        <!-- 操作列 -->
        <template v-else-if="column.key === 'actions'">
          <a-space>
            <a-button type="link" size="small" @click="editQuotaItems(record)">
              定额
            </a-button>
            <a-popconfirm
              title="确定删除这个清单项吗？"
              @confirm="deleteBillItem(record)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, ImportOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  unitProject: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update'])

// 响应式数据
const searchText = ref('')
const pagination = ref({
  current: 1,
  pageSize: 50,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列定义
const columns = [
  {
    title: '行号',
    key: 'sequence',
    width: 60,
    fixed: 'left'
  },
  {
    title: '项目编码',
    key: 'code',
    width: 150,
    fixed: 'left'
  },
  {
    title: '项目名称',
    key: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: '项目特征',
    key: 'characteristics',
    width: 250
  },
  {
    title: '计量单位',
    key: 'unit',
    width: 100
  },
  {
    title: '工程量',
    key: 'quantity',
    width: 120,
    align: 'right'
  },
  {
    title: '综合单价(元)',
    key: 'unit_price',
    width: 120,
    align: 'right'
  },
  {
    title: '合价(元)',
    key: 'total_price',
    width: 120,
    align: 'right'
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 计算属性
const filteredBillItems = computed(() => {
  if (!props.unitProject?.bill_items) return []
  
  let items = [...props.unitProject.bill_items]
  
  // 搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    items = items.filter(item => 
      item.name?.toLowerCase().includes(search) ||
      item.code?.toLowerCase().includes(search) ||
      item.characteristics?.toLowerCase().includes(search)
    )
  }
  
  return items
})

// 添加清单项
const addBillItem = () => {
  const newItem = {
    sequence: (props.unitProject.bill_items?.length || 0) + 1,
    code: '',
    name: '',
    characteristics: '',
    unit: '',
    quantity: 0,
    unit_price: 0,
    total_price: 0,
    quota_items: []
  }
  
  const updatedProject = { ...props.unitProject }
  if (!updatedProject.bill_items) {
    updatedProject.bill_items = []
  }
  updatedProject.bill_items.push(newItem)
  
  emitUpdate(updatedProject)
}

// 删除清单项
const deleteBillItem = (item) => {
  const updatedProject = { ...props.unitProject }
  const index = updatedProject.bill_items.findIndex(i => i.sequence === item.sequence)
  if (index > -1) {
    updatedProject.bill_items.splice(index, 1)
    // 重新编号
    updatedProject.bill_items.forEach((item, idx) => {
      item.sequence = idx + 1
    })
    emitUpdate(updatedProject)
    message.success('清单项删除成功')
  }
}

// 单元格变化处理
const onCellChange = (record) => {
  emitUpdate(props.unitProject)
}

// 工程量变化处理
const onQuantityChange = (record) => {
  record.total_price = record.quantity * record.unit_price
  emitUpdate(props.unitProject)
}

// 单价变化处理
const onUnitPriceChange = (record) => {
  record.total_price = record.quantity * record.unit_price
  emitUpdate(props.unitProject)
}

// 编辑定额项
const editQuotaItems = (record) => {
  // TODO: 打开定额项编辑对话框
  message.info('定额项编辑功能待开发')
}

// 导入清单
const importBillItems = () => {
  // TODO: 实现清单导入功能
  message.info('清单导入功能待开发')
}

// 搜索处理
const onSearch = (value) => {
  searchText.value = value
}

// 格式化货币
const formatCurrency = (value) => {
  if (typeof value !== 'number') return '0.00'
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 发送更新事件
const emitUpdate = (updatedProject) => {
  // 重新计算费用汇总
  const billCost = updatedProject.bill_items?.reduce((sum, item) => sum + (item.total_price || 0), 0) || 0
  
  if (!updatedProject.cost_summary) {
    updatedProject.cost_summary = {
      bill_cost: 0,
      measure_cost: 0,
      other_cost: 0,
      tax: 0,
      total_cost: 0
    }
  }
  
  updatedProject.cost_summary.bill_cost = billCost
  updatedProject.cost_summary.total_cost = 
    updatedProject.cost_summary.bill_cost + 
    updatedProject.cost_summary.measure_cost + 
    updatedProject.cost_summary.other_cost + 
    updatedProject.cost_summary.tax
  
  emit('update', updatedProject)
}

// 监听单位工程变化
watch(() => props.unitProject, () => {
  // 重置搜索
  searchText.value = ''
}, { deep: true })
</script>

<style scoped>
.bill-items-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.price-cell {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #1890ff;
}

:deep(.ant-table) {
  flex: 1;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
}

:deep(.ant-input-number) {
  border: none;
  box-shadow: none;
}

:deep(.ant-input-number:hover) {
  border-color: #1890ff;
}

:deep(.ant-input-number:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-select) {
  border: none;
}

:deep(.ant-select:hover .ant-select-selector) {
  border-color: #1890ff;
}

:deep(.ant-select-focused .ant-select-selector) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>
