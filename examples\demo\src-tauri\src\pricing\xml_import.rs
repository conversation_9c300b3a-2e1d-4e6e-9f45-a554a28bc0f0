/// XML 导入功能模块
/// 
/// 实现从其他软件（如惠招标）导出的 XML 文件导入功能
/// 支持工程概况、分部分项、措施项目等数据的解析和映射

use super::models::*;
use anyhow::{Result, anyhow};
use quick_xml::de::from_str;
use quick_xml::events::Event;
use quick_xml::Reader;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use uuid::Uuid;

/// XML 导入的项目数据结构
#[derive(Debug, Deserialize)]
pub struct XmlProject {
    #[serde(rename = "ProjectInfo")]
    pub project_info: Option<XmlProjectInfo>,
    #[serde(rename = "BillItems")]
    pub bill_items: Option<Vec<XmlBillItem>>,
    #[serde(rename = "MeasureItems")]
    pub measure_items: Option<Vec<XmlMeasureItem>>,
    #[serde(rename = "OtherItems")]
    pub other_items: Option<Vec<XmlOtherItem>>,
}

/// XML 项目信息
#[derive(Debug, Deserialize)]
pub struct XmlProjectInfo {
    #[serde(rename = "Name")]
    pub name: Option<String>,
    #[serde(rename = "Code")]
    pub code: Option<String>,
    #[serde(rename = "Description")]
    pub description: Option<String>,
    #[serde(rename = "TaxMethod")]
    pub tax_method: Option<String>,
}

/// XML 分部分项工程
#[derive(Debug, Deserialize)]
pub struct XmlBillItem {
    #[serde(rename = "Code")]
    pub code: Option<String>,
    #[serde(rename = "Name")]
    pub name: Option<String>,
    #[serde(rename = "Characteristics")]
    pub characteristics: Option<String>,
    #[serde(rename = "Unit")]
    pub unit: Option<String>,
    #[serde(rename = "Quantity")]
    pub quantity: Option<String>,
    #[serde(rename = "UnitPrice")]
    pub unit_price: Option<String>,
}

/// XML 措施项目
#[derive(Debug, Deserialize)]
pub struct XmlMeasureItem {
    #[serde(rename = "Code")]
    pub code: Option<String>,
    #[serde(rename = "Name")]
    pub name: Option<String>,
    #[serde(rename = "Rate")]
    pub rate: Option<String>,
    #[serde(rename = "Amount")]
    pub amount: Option<String>,
}

/// XML 其他项目
#[derive(Debug, Deserialize)]
pub struct XmlOtherItem {
    #[serde(rename = "Name")]
    pub name: Option<String>,
    #[serde(rename = "Type")]
    pub item_type: Option<String>,
    #[serde(rename = "Amount")]
    pub amount: Option<String>,
}

/// XML 导入器
pub struct XmlImporter;

impl XmlImporter {
    /// 从 XML 文件导入项目
    pub fn import_from_file(file_path: &Path) -> Result<PricingProject> {
        // 读取 XML 文件
        let xml_content = fs::read_to_string(file_path)?;
        
        // 解析 XML
        Self::import_from_xml_string(&xml_content)
    }
    
    /// 从 XML 字符串导入项目
    pub fn import_from_xml_string(xml_content: &str) -> Result<PricingProject> {
        // 尝试解析 XML
        let xml_project: XmlProject = from_str(xml_content)
            .map_err(|e| anyhow!("XML 解析失败: {}", e))?;
        
        // 转换为内部数据结构
        Self::convert_xml_to_project(xml_project)
    }
    
    /// 将 XML 数据转换为项目数据
    fn convert_xml_to_project(xml_project: XmlProject) -> Result<PricingProject> {
        let mut project = PricingProject::default();
        
        // 转换项目基本信息
        if let Some(xml_info) = xml_project.project_info {
            project.info = Self::convert_project_info(xml_info);
        }
        
        // 创建默认的单项工程和单位工程
        let mut single_project = SingleProject {
            id: Uuid::new_v4().to_string(),
            name: "单项工程".to_string(),
            unit_projects: vec![],
        };
        
        let mut unit_project = UnitProject {
            id: Uuid::new_v4().to_string(),
            name: "单位工程".to_string(),
            bill_items: vec![],
            measure_items: vec![],
            other_items: vec![],
            cost_summary: CostSummary::default(),
        };
        
        // 转换分部分项工程
        if let Some(xml_bills) = xml_project.bill_items {
            unit_project.bill_items = Self::convert_bill_items(xml_bills);
        }
        
        // 转换措施项目
        if let Some(xml_measures) = xml_project.measure_items {
            unit_project.measure_items = Self::convert_measure_items(xml_measures);
        }
        
        // 转换其他项目
        if let Some(xml_others) = xml_project.other_items {
            unit_project.other_items = Self::convert_other_items(xml_others);
        }
        
        // 计算费用汇总
        unit_project.cost_summary = Self::calculate_cost_summary(&unit_project);
        
        single_project.unit_projects.push(unit_project);
        project.single_projects.push(single_project);
        
        Ok(project)
    }
    
    /// 转换项目基本信息
    fn convert_project_info(xml_info: XmlProjectInfo) -> ProjectInfo {
        let tax_method = match xml_info.tax_method.as_deref() {
            Some("简易计税") => TaxMethod::Simple,
            _ => TaxMethod::General,
        };
        
        ProjectInfo {
            name: xml_info.name.unwrap_or_else(|| "导入项目".to_string()),
            code: xml_info.code.unwrap_or_default(),
            list_standard: "".to_string(),
            quota_standard: "".to_string(),
            tax_method,
            overview: ProjectOverview {
                description: xml_info.description.unwrap_or_default(),
                characteristics: "".to_string(),
                other_info: HashMap::new(),
            },
        }
    }
    
    /// 转换分部分项工程
    fn convert_bill_items(xml_bills: Vec<XmlBillItem>) -> Vec<BillItem> {
        xml_bills
            .into_iter()
            .enumerate()
            .map(|(index, xml_bill)| {
                let quantity = Self::parse_float(&xml_bill.quantity).unwrap_or(0.0);
                let unit_price = Self::parse_float(&xml_bill.unit_price).unwrap_or(0.0);
                
                BillItem {
                    sequence: (index + 1).to_string(),
                    code: xml_bill.code.unwrap_or_default(),
                    name: xml_bill.name.unwrap_or_default(),
                    characteristics: xml_bill.characteristics.unwrap_or_default(),
                    unit: xml_bill.unit.unwrap_or_default(),
                    quantity,
                    unit_price,
                    total_price: quantity * unit_price,
                    quota_items: vec![], // XML 中通常不包含定额信息
                }
            })
            .collect()
    }
    
    /// 转换措施项目
    fn convert_measure_items(xml_measures: Vec<XmlMeasureItem>) -> Vec<MeasureItem> {
        xml_measures
            .into_iter()
            .map(|xml_measure| {
                let rate = Self::parse_float(&xml_measure.rate).unwrap_or(0.0);
                let amount = Self::parse_float(&xml_measure.amount).unwrap_or(0.0);
                
                MeasureItem {
                    code: xml_measure.code.unwrap_or_default(),
                    name: xml_measure.name.unwrap_or_default(),
                    calculation_base: "".to_string(),
                    rate,
                    amount,
                }
            })
            .collect()
    }
    
    /// 转换其他项目
    fn convert_other_items(xml_others: Vec<XmlOtherItem>) -> Vec<OtherItem> {
        xml_others
            .into_iter()
            .map(|xml_other| {
                let item_type = match xml_other.item_type.as_deref() {
                    Some("暂列金额") => OtherItemType::ProvisionalSum,
                    Some("专业工程暂估价") => OtherItemType::ProfessionalEstimate,
                    Some("计日工") => OtherItemType::DayWork,
                    Some("总承包服务费") => OtherItemType::GeneralContractorFee,
                    _ => OtherItemType::ProvisionalSum,
                };
                
                let amount = Self::parse_float(&xml_other.amount).unwrap_or(0.0);
                
                OtherItem {
                    name: xml_other.name.unwrap_or_default(),
                    item_type,
                    amount,
                    remark: "".to_string(),
                }
            })
            .collect()
    }
    
    /// 计算费用汇总
    fn calculate_cost_summary(unit_project: &UnitProject) -> CostSummary {
        let bill_cost: f64 = unit_project.bill_items.iter()
            .map(|item| item.total_price)
            .sum();
        
        let measure_cost: f64 = unit_project.measure_items.iter()
            .map(|item| item.amount)
            .sum();
        
        let other_cost: f64 = unit_project.other_items.iter()
            .map(|item| item.amount)
            .sum();
        
        let subtotal = bill_cost + measure_cost + other_cost;
        let tax = subtotal * 0.09; // 假设税率为 9%
        let total_cost = subtotal + tax;
        
        CostSummary {
            bill_cost,
            measure_cost,
            other_cost,
            tax,
            total_cost,
        }
    }
    
    /// 解析浮点数字符串
    fn parse_float(value: &Option<String>) -> Option<f64> {
        value.as_ref()?.parse().ok()
    }
    
    /// 验证 XML 文件格式
    pub fn validate_xml_file(file_path: &Path) -> Result<bool> {
        if !file_path.exists() {
            return Ok(false);
        }
        
        // 检查文件扩展名
        if file_path.extension().and_then(|s| s.to_str()) != Some("xml") {
            return Ok(false);
        }
        
        // 尝试读取和解析 XML 文件
        let xml_content = fs::read_to_string(file_path)?;
        
        // 简单的 XML 格式验证
        let mut reader = Reader::from_str(&xml_content);
        reader.config_mut().trim_text(true);
        
        let mut buf = Vec::new();
        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(_)) | Ok(Event::Empty(_)) | Ok(Event::Text(_)) | Ok(Event::End(_)) => {},
                Ok(Event::Eof) => break,
                Err(_) => return Ok(false),
                _ => {},
            }
            buf.clear();
        }
        
        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_xml_import() {
        let xml_content = r#"
        <Project>
            <ProjectInfo>
                <Name>测试项目</Name>
                <Code>TEST001</Code>
                <Description>这是一个测试项目</Description>
                <TaxMethod>一般计税</TaxMethod>
            </ProjectInfo>
            <BillItems>
                <BillItem>
                    <Code>010101001001</Code>
                    <Name>平整场地</Name>
                    <Unit>m²</Unit>
                    <Quantity>1000</Quantity>
                    <UnitPrice>5.5</UnitPrice>
                </BillItem>
            </BillItems>
        </Project>
        "#;
        
        let result = XmlImporter::import_from_xml_string(xml_content);
        assert!(result.is_ok());
        
        let project = result.unwrap();
        assert_eq!(project.info.name, "测试项目");
        assert_eq!(project.info.code, "TEST001");
        assert_eq!(project.single_projects.len(), 1);
        assert_eq!(project.single_projects[0].unit_projects[0].bill_items.len(), 1);
    }
}
