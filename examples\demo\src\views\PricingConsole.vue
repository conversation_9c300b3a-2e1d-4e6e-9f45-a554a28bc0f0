<template>
  <div class="pricing-console">
    <!-- 头部标题 -->
    <div class="console-header">
      <h1 class="title">计价软件 v0.0.6</h1>
      <p class="subtitle">专业的工程造价管理系统</p>
    </div>

    <!-- 主要功能区域 -->
    <div class="console-main">
      <!-- 左侧功能按钮 -->
      <div class="function-panel">
        <div class="function-group">
          <h3>项目管理</h3>
          <a-button 
            type="primary" 
            size="large" 
            class="function-btn"
            @click="showNewProjectModal"
          >
            <template #icon>
              <PlusOutlined />
            </template>
            新建预算项目
          </a-button>
          
          <a-button 
            size="large" 
            class="function-btn"
            @click="openProject"
          >
            <template #icon>
              <FolderOpenOutlined />
            </template>
            打开项目
          </a-button>
          
          <a-button 
            size="large" 
            class="function-btn"
            @click="importXmlProject"
          >
            <template #icon>
              <ImportOutlined />
            </template>
            导入项目 (XML)
          </a-button>
        </div>
      </div>

      <!-- 右侧最近打开 -->
      <div class="recent-panel">
        <h3>最近打开</h3>
        <div class="recent-list" v-if="recentFiles.length > 0">
          <div 
            v-for="file in recentFiles" 
            :key="file.path"
            class="recent-item"
            @click="openRecentFile(file)"
          >
            <div class="recent-info">
              <div class="project-name">{{ file.project_name }}</div>
              <div class="project-path">{{ file.path }}</div>
              <div class="last-opened">{{ formatDate(file.last_opened) }}</div>
            </div>
            <a-button 
              type="text" 
              size="small"
              @click.stop="removeRecentFile(file)"
            >
              <template #icon>
                <CloseOutlined />
              </template>
            </a-button>
          </div>
        </div>
        <div v-else class="empty-recent">
          <a-empty description="暂无最近打开的项目" />
        </div>
      </div>
    </div>

    <!-- 新建项目模态框 -->
    <a-modal
      v-model:open="newProjectModalVisible"
      title="新建预算项目"
      @ok="createNewProject"
      @cancel="cancelNewProject"
      :confirm-loading="creating"
    >
      <a-form
        ref="newProjectForm"
        :model="newProjectData"
        :rules="newProjectRules"
        layout="vertical"
      >
        <a-form-item label="项目名称" name="name">
          <a-input v-model:value="newProjectData.name" placeholder="请输入项目名称" />
        </a-form-item>
        
        <a-form-item label="项目编码" name="code">
          <a-input v-model:value="newProjectData.code" placeholder="请输入项目编码" />
        </a-form-item>
        
        <a-form-item label="清单标准" name="listStandard">
          <a-select v-model:value="newProjectData.listStandard" placeholder="请选择清单标准">
            <a-select-option value="GB50500-2013">GB50500-2013</a-select-option>
            <a-select-option value="GB50500-2008">GB50500-2008</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="定额标准" name="quotaStandard">
          <a-select v-model:value="newProjectData.quotaStandard" placeholder="请选择定额标准">
            <a-select-option value="地方定额">地方定额</a-select-option>
            <a-select-option value="行业定额">行业定额</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'
import { 
  PlusOutlined, 
  FolderOpenOutlined, 
  ImportOutlined,
  CloseOutlined 
} from '@ant-design/icons-vue'

const router = useRouter()

// 响应式数据
const recentFiles = ref([])
const newProjectModalVisible = ref(false)
const creating = ref(false)
const newProjectForm = ref()

// 新建项目表单数据
const newProjectData = ref({
  name: '',
  code: '',
  listStandard: '',
  quotaStandard: ''
})

// 表单验证规则
const newProjectRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入项目编码', trigger: 'blur' }
  ],
  listStandard: [
    { required: true, message: '请选择清单标准', trigger: 'change' }
  ],
  quotaStandard: [
    { required: true, message: '请选择定额标准', trigger: 'change' }
  ]
}

// 加载最近文件
const loadRecentFiles = async () => {
  try {
    const files = await invoke('get_recent_projects')
    recentFiles.value = files || []
  } catch (error) {
    console.error('加载最近文件失败:', error)
  }
}

// 显示新建项目模态框
const showNewProjectModal = () => {
  newProjectModalVisible.value = true
  // 重置表单
  newProjectData.value = {
    name: '',
    code: '',
    listStandard: '',
    quotaStandard: ''
  }
}

// 创建新项目
const createNewProject = async () => {
  try {
    await newProjectForm.value.validate()
    creating.value = true
    
    const filePath = await invoke('create_new_project', {
      projectInfo: newProjectData.value
    })
    
    message.success('项目创建成功')
    newProjectModalVisible.value = false
    
    // 跳转到工作台
    router.push('/pricing-workbench')
    
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return
    }
    console.error('创建项目失败:', error)
    message.error(`创建项目失败: ${error}`)
  } finally {
    creating.value = false
  }
}

// 取消新建项目
const cancelNewProject = () => {
  newProjectModalVisible.value = false
}

// 打开项目
const openProject = async () => {
  try {
    const projectData = await invoke('open_project_file')
    message.success('项目打开成功')
    
    // 刷新最近文件列表
    await loadRecentFiles()
    
    // 跳转到工作台
    router.push('/pricing-workbench')
    
  } catch (error) {
    if (error !== '用户取消了文件选择') {
      console.error('打开项目失败:', error)
      message.error(`打开项目失败: ${error}`)
    }
  }
}

// 导入 XML 项目
const importXmlProject = async () => {
  try {
    const projectData = await invoke('import_xml_project')
    message.success('XML 项目导入成功，请保存项目文件')
    
    // 跳转到工作台
    router.push('/pricing-workbench')
    
  } catch (error) {
    if (error !== '用户取消了文件选择') {
      console.error('导入 XML 项目失败:', error)
      message.error(`导入 XML 项目失败: ${error}`)
    }
  }
}

// 打开最近文件
const openRecentFile = async (file) => {
  try {
    const projectData = await invoke('open_project_file', {
      filePath: file.path
    })
    message.success('项目打开成功')
    
    // 刷新最近文件列表
    await loadRecentFiles()
    
    // 跳转到工作台
    router.push('/pricing-workbench')
    
  } catch (error) {
    console.error('打开最近文件失败:', error)
    message.error(`打开文件失败: ${error}`)
    
    // 如果文件不存在，从列表中移除
    if (error.includes('文件不存在')) {
      await loadRecentFiles()
    }
  }
}

// 移除最近文件
const removeRecentFile = async (file) => {
  // 这里可以调用后端接口移除文件，暂时只是前端移除
  const index = recentFiles.value.findIndex(f => f.path === file.path)
  if (index > -1) {
    recentFiles.value.splice(index, 1)
  }
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadRecentFiles()
})
</script>

<style scoped>
.pricing-console {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px;
  display: flex;
  flex-direction: column;
}

.console-header {
  text-align: center;
  margin-bottom: 60px;
  color: white;
}

.title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.console-main {
  display: flex;
  gap: 60px;
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.function-panel {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.function-group h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 20px;
}

.function-btn {
  width: 100%;
  height: 60px;
  margin-bottom: 16px;
  font-size: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-panel {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.recent-panel h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 20px;
}

.recent-list {
  max-height: 400px;
  overflow-y: auto;
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.recent-item:hover {
  background: #f8f9fa;
  border-color: #1890ff;
}

.recent-info {
  flex: 1;
}

.project-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.project-path {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  word-break: break-all;
}

.last-opened {
  font-size: 12px;
  color: #999;
}

.empty-recent {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}
</style>
