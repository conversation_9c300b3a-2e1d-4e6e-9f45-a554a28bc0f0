import { copyFileSync, mkdirSync, existsSync, readdirSync, statSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const sourceDir = join(__dirname, 'dist')
const targetDir = join(__dirname, '../../dist/pricing-software')

function copyDir(src, dest) {
  if (!existsSync(dest)) {
    mkdirSync(dest, { recursive: true })
  }

  const files = readdirSync(src)
  
  for (const file of files) {
    const srcPath = join(src, file)
    const destPath = join(dest, file)
    
    if (statSync(srcPath).isDirectory()) {
      copyDir(srcPath, destPath)
    } else {
      copyFileSync(srcPath, destPath)
    }
  }
}

if (existsSync(sourceDir)) {
  copyDir(sourceDir, targetDir)
  console.log('✅ 计价软件模块构建产物已复制到主应用')
} else {
  console.log('❌ 计价软件模块构建产物不存在，请先运行 npm run build')
}
