<template>
  <div class="console">
    <!-- 顶部工具栏 -->
    <div class="console-toolbar">
      <div class="toolbar-left">
        <a-button @click="goBack" type="text">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回主页
        </a-button>
        <a-divider type="vertical" />
        <h3>项目控制台</h3>
      </div>
      
      <div class="toolbar-right">
        <a-button @click="refreshRecentFiles">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="console-content">
      <!-- 左侧操作面板 -->
      <div class="action-panel">
        <a-card title="项目操作" size="small">
          <a-space direction="vertical" style="width: 100%;" :size="16">
            <a-button 
              type="primary" 
              size="large" 
              block
              @click="showNewProjectModal"
            >
              <template #icon>
                <PlusOutlined />
              </template>
              新建预算项目
            </a-button>
            
            <a-button 
              size="large" 
              block
              @click="openProject"
            >
              <template #icon>
                <FolderOpenOutlined />
              </template>
              打开项目文件
            </a-button>
            
            <a-button 
              size="large" 
              block
              @click="importXmlProject"
            >
              <template #icon>
                <ImportOutlined />
              </template>
              导入 XML 项目
            </a-button>
          </a-space>
        </a-card>
      </div>

      <!-- 右侧最近项目 -->
      <div class="recent-panel">
        <a-card title="最近项目" size="small">
          <div class="recent-list" v-if="recentFiles.length > 0">
            <div 
              v-for="file in recentFiles" 
              :key="file.path"
              class="recent-item"
              @click="openRecentFile(file)"
            >
              <div class="recent-info">
                <div class="project-name">{{ file.project_name }}</div>
                <div class="project-path">{{ file.path }}</div>
                <div class="last-opened">{{ formatDate(file.last_opened) }}</div>
              </div>
              <a-button 
                type="text" 
                size="small"
                @click.stop="removeRecentFile(file)"
                danger
              >
                <template #icon>
                  <CloseOutlined />
                </template>
              </a-button>
            </div>
          </div>
          <a-empty v-else description="暂无最近项目" />
        </a-card>
      </div>
    </div>

    <!-- 新建项目模态框 -->
    <a-modal
      v-model:open="newProjectModalVisible"
      title="新建预算项目"
      @ok="createNewProject"
      @cancel="cancelNewProject"
      :confirm-loading="creating"
      width="600px"
    >
      <a-form
        ref="newProjectForm"
        :model="newProjectData"
        :rules="newProjectRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="项目名称" name="name">
              <a-input v-model:value="newProjectData.name" placeholder="请输入项目名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="项目编码" name="code">
              <a-input v-model:value="newProjectData.code" placeholder="请输入项目编码" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="清单标准" name="listStandard">
              <a-select v-model:value="newProjectData.listStandard" placeholder="请选择清单标准">
                <a-select-option value="GB50500-2013">GB50500-2013</a-select-option>
                <a-select-option value="GB50500-2008">GB50500-2008</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="定额标准" name="quotaStandard">
              <a-select v-model:value="newProjectData.quotaStandard" placeholder="请选择定额标准">
                <a-select-option value="地方定额">地方定额</a-select-option>
                <a-select-option value="行业定额">行业定额</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'
import { 
  ArrowLeftOutlined,
  ReloadOutlined,
  PlusOutlined, 
  FolderOpenOutlined, 
  ImportOutlined,
  CloseOutlined 
} from '@ant-design/icons-vue'

const router = useRouter()

// 响应式数据
const recentFiles = ref([])
const newProjectModalVisible = ref(false)
const creating = ref(false)
const newProjectForm = ref()

// 新建项目表单数据
const newProjectData = ref({
  name: '',
  code: '',
  listStandard: '',
  quotaStandard: ''
})

// 表单验证规则
const newProjectRules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入项目编码', trigger: 'blur' }],
  listStandard: [{ required: true, message: '请选择清单标准', trigger: 'change' }],
  quotaStandard: [{ required: true, message: '请选择定额标准', trigger: 'change' }]
}

// 返回主页
const goBack = () => {
  router.push('/')
}

// 刷新最近文件
const refreshRecentFiles = () => {
  loadRecentFiles()
  message.success('已刷新')
}

// 加载最近文件
const loadRecentFiles = async () => {
  try {
    const files = await invoke('get_recent_projects')
    recentFiles.value = files || []
  } catch (error) {
    console.error('加载最近文件失败:', error)
  }
}

// 显示新建项目模态框
const showNewProjectModal = () => {
  newProjectModalVisible.value = true
  newProjectData.value = {
    name: '',
    code: '',
    listStandard: '',
    quotaStandard: ''
  }
}

// 创建新项目
const createNewProject = async () => {
  try {
    await newProjectForm.value.validate()
    creating.value = true
    
    const filePath = await invoke('create_new_project', {
      projectInfo: newProjectData.value
    })
    
    message.success('项目创建成功')
    newProjectModalVisible.value = false
    
    // 跳转到工作台
    router.push('/workbench')
    
  } catch (error) {
    if (error.errorFields) {
      return
    }
    console.error('创建项目失败:', error)
    message.error(`创建项目失败: ${error}`)
  } finally {
    creating.value = false
  }
}

// 取消新建项目
const cancelNewProject = () => {
  newProjectModalVisible.value = false
}

// 打开项目
const openProject = async () => {
  try {
    const projectData = await invoke('open_project_file')
    message.success('项目打开成功')
    
    await loadRecentFiles()
    router.push('/workbench')
    
  } catch (error) {
    if (error !== '用户取消了文件选择') {
      console.error('打开项目失败:', error)
      message.error(`打开项目失败: ${error}`)
    }
  }
}

// 导入 XML 项目
const importXmlProject = async () => {
  try {
    const projectData = await invoke('import_xml_project')
    message.success('XML 项目导入成功，请保存项目文件')
    
    router.push('/workbench')
    
  } catch (error) {
    if (error !== '用户取消了文件选择') {
      console.error('导入 XML 项目失败:', error)
      message.error(`导入 XML 项目失败: ${error}`)
    }
  }
}

// 打开最近文件
const openRecentFile = async (file) => {
  try {
    const projectData = await invoke('open_project_file', {
      filePath: file.path
    })
    message.success('项目打开成功')
    
    await loadRecentFiles()
    router.push('/workbench')
    
  } catch (error) {
    console.error('打开最近文件失败:', error)
    message.error(`打开文件失败: ${error}`)
    
    if (error.includes('文件不存在')) {
      await loadRecentFiles()
    }
  }
}

// 移除最近文件
const removeRecentFile = async (file) => {
  const index = recentFiles.value.findIndex(f => f.path === file.path)
  if (index > -1) {
    recentFiles.value.splice(index, 1)
    message.success('已从列表中移除')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  loadRecentFiles()
})
</script>

<style scoped>
.console {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.console-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left h3 {
  margin: 0;
  color: #333;
}

.console-content {
  flex: 1;
  display: flex;
  gap: 24px;
  padding: 24px;
  overflow: hidden;
}

.action-panel {
  width: 300px;
  flex-shrink: 0;
}

.recent-panel {
  flex: 1;
  overflow: hidden;
}

.recent-list {
  max-height: 500px;
  overflow-y: auto;
}

.recent-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.recent-item:hover {
  background: #f8f9fa;
  border-color: #1890ff;
}

.recent-info {
  flex: 1;
}

.project-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.project-path {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  word-break: break-all;
}

.last-opened {
  font-size: 12px;
  color: #999;
}
</style>
