/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    ASpace: typeof import('ant-design-vue/es')['Space']
    BillItemsTable: typeof import('./src/components/BillItemsTable.vue')['default']
    CostSummary: typeof import('./src/components/CostSummary.vue')['default']
    MeasureItemsTable: typeof import('./src/components/MeasureItemsTable.vue')['default']
    OtherItemsTable: typeof import('./src/components/OtherItemsTable.vue')['default']
    ProjectOverview: typeof import('./src/components/ProjectOverview.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
