# Frontend 技术文档

该文档为 ModuForge-RS 演示应用的前端开发提供技术指导，供 Claude Code 在进行前端编码时参考。

## 项目概览

这是一个基于 Vue 3 + Tauri 的造价管理系统演示应用，采用 Workspace 微前端架构，支持多窗口管理和模块化开发。

## 技术栈

### 核心技术
- **Vue 3.5.13**: 使用 Composition API 和 `<script setup>` 语法
- **Vue Router 4.5.1**: 客户端路由管理
- **Pinia 3.0.3**: 状态管理
- **Ant Design Vue 4.2.6**: UI 组件库
- **Tauri 2.5.0**: 桌面应用框架
- **Vite 6.2.4**: 构建工具和开发服务器

### 开发工具
- **unplugin-auto-import**: 自动导入 API
- **unplugin-vue-components**: 自动导入组件
- **Less 4.4.0**: CSS 预处理器
- **vue-devtools**: Vue 开发工具

## 项目结构

```
demo/
├── src/                          # 主应用源码
│   ├── App.vue                   # 根组件
│   ├── main.js                   # 应用入口
│   ├── router/                   # 路由配置
│   ├── views/                    # 页面组件
│   ├── components/               # 通用组件
│   ├── composables/              # 组合式函数
│   ├── utils/                    # 工具函数
│   └── assets/                   # 静态资源
├── packages/                     # 微前端子应用
│   ├── main-shell/              # 主容器应用
│   ├── rough-estimate/          # 概算模块
│   ├── shared-components/       # 共享组件库
│   └── budget/                  # 预算模块
├── src-tauri/                   # Tauri 后端
└── public/                      # 公共静态文件
```

## 核心配置

### Vite 配置特性
- **自动导入**: Ant Design Vue 组件和 API 自动导入
- **别名配置**: `@` 指向 `src` 目录
- **Less 支持**: 预处理器配置和变量覆盖
- **环境变量**: 
  - `VITE_APP_TITLE`: "造价管理系统"
  - `VITE_API_BASE_URL`: "http://localhost:20008/api"
- **构建插件**: 自定义插件复制 splashscreen.html 和子模块构建产物

### 开发服务器
- **端口**: 5173
- **代理**: API 请求代理到后端服务

## 路由系统

### 路由配置
主要路由及其功能：

```javascript
const routes = [
  { path: '/', redirect: '/pricing-console' },
  { path: '/dashboard', component: Dashboard, meta: { title: '工作台' } },
  { path: '/pricing-console', component: PricingConsole, meta: { title: '计价软件控制台' } },
  { path: '/pricing-workbench', component: PricingWorkbench, meta: { title: '计价软件工作台' } },
  // ... 其他路由
]
```

### 路由守卫
- 自动设置页面标题格式：`${页面标题} - 造价管理系统`

## 组件架构

### 页面组件 (views/)
- **Dashboard.vue**: 工作台主页
- **PricingConsole.vue**: 计价软件控制台
- **PricingWorkbench.vue**: 计价软件工作台
- **EstimateDemo.vue**: 概算演示
- **FormPage.vue**: 表单页面
- **DataPage.vue**: 数据查看器
- **SettingsPage.vue**: 系统设置
- **TableTest.vue**: 表格测试

### 通用组件 (components/)
- **WindowManagerDemo.vue**: 窗体管理演示
- **pricing/**: 计价相关组件
  - **ProjectOverview.vue**: 项目概览
  - **MeasureItemsTable.vue**: 计量项目表格
  - **BillItemsTable.vue**: 清单项目表格
  - **OtherItemsTable.vue**: 其他项目表格
  - **CostSummary.vue**: 费用汇总

### Composables (组合式函数)
- **useWindowModal.js**: 窗口模态框管理

## 微前端架构

### Workspace 结构
项目采用 npm workspaces 管理多个子应用：

1. **main-shell**: 主容器应用
   - 路由: Dashboard, Budget, RoughEstimate, Settlement 等
   - 作为其他微应用的容器

2. **rough-estimate**: 概算模块
   - 独立的概算计算功能
   - 支持多窗口操作

3. **shared-components**: 共享组件库
   - 通用组件: CostForm, CostTable, FormWindow 等
   - 通用 Composables: 成本计算、窗口管理、数据交换等
   - 布局组件: AppHeader, SimpleHeader

### 构建流程
```bash
# 开发模式
npm run dev:all          # 启动所有子应用开发服务器

# 构建流程
npm run build:packages   # 构建所有子应用
npm run build           # 构建主应用并复制子应用构建产物
```

## Tauri 集成

### 后端命令
- **定价相关**: djgc, fbfx_csxm, gcxm, rcj 等命令
- **文件操作**: XML 导入、文件 I/O 操作
- **协作编辑**: 基于 CRDT 的实时协作功能

### 窗口管理
- 支持多窗口创建和管理
- 窗口间数据交换
- 模态和非模态窗口支持

## 开发规范

### Vue 3 编码约定
1. **组件定义**: 使用 `<script setup>` 语法
2. **响应式数据**: 优先使用 `ref()` 和 `reactive()`
3. **组件通信**: 使用 `defineProps()` 和 `defineEmits()`
4. **生命周期**: 使用组合式 API 生命周期钩子

### 样式规范
1. **CSS 预处理器**: 使用 Less，支持变量和嵌套
2. **组件样式**: 使用 `<style scoped>` 避免样式污染
3. **主题配置**: 通过 Ant Design Vue 的 ConfigProvider 统一主题

### 命名约定
1. **文件命名**: PascalCase (如: PricingConsole.vue)
2. **组件名**: PascalCase
3. **路由名**: PascalCase
4. **变量名**: camelCase

## API 集成

### 后端通信
- **API 基址**: `http://localhost:20008/api`
- **Tauri Commands**: 使用 `@tauri-apps/api` 调用后端命令
- **数据格式**: 主要使用 JSON 格式

### 数据模型
项目主要处理造价管理相关数据：
- 项目信息
- 计量项目数据
- 清单项目数据
- 费用汇总数据

## 开发工作流

### 本地开发
```bash
# 安装依赖
npm install

# 启动开发服务器 (仅前端)
npm run dev

# 启动 Tauri 开发环境 (前端 + 后端)
npm run tauri:dev

# 启动所有微应用
npm run dev:all
```

### 构建部署
```bash
# 构建生产版本
npm run build

# 构建 Tauri 应用
npm run tauri:build
```

### 调试工具
- **Vue DevTools**: 组件调试和状态检查
- **Vite DevTools**: 构建过程调试
- **Tauri DevTools**: 桌面应用调试

## 性能优化

### 代码分割
- 路由级别的懒加载: `() => import('../views/Component.vue')`
- 组件级别的动态导入

### 构建优化
- 自动导入减少包体积
- Tree-shaking 移除未使用代码
- 微前端架构支持按需加载

## 常见开发任务

### 添加新页面
1. 在 `src/views/` 创建 Vue 组件
2. 在 `src/router/index.js` 添加路由配置
3. 设置合适的 meta 信息 (title)

### 添加新组件
1. 在 `src/components/` 创建组件文件
2. 使用 `<script setup>` 语法
3. 添加必要的 props 和 emits 定义

### 集成 Tauri 命令
1. 在 Rust 后端定义命令
2. 在前端使用 `invoke()` 调用命令
3. 处理异步结果和错误

### 样式自定义
1. 在组件中使用 Less 语法
2. 通过 ConfigProvider 自定义主题变量
3. 使用 scoped 样式避免冲突

## 故障排除

### 常见问题
1. **热重载问题**: 参考 `HOT_RELOAD_GUIDE.md`
2. **窗口管理**: 参考 `WINDOW_MANAGEMENT_GUIDE.md`
3. **子窗口修复**: 参考 `CHILD_WINDOW_FIX.md`
4. **打包问题**: 参考 `PACKAGING_GUIDE.md`

### 调试策略
1. 检查浏览器控制台错误
2. 使用 Vue DevTools 检查组件状态
3. 查看 Tauri 日志输出
4. 检查网络请求状态

## 最佳实践

1. **组件设计**: 保持组件单一职责，提高复用性
2. **状态管理**: 合理使用 Pinia 管理全局状态
3. **错误处理**: 统一错误处理和用户反馈
4. **性能监控**: 使用 Vue DevTools 监控组件性能
5. **代码规范**: 保持一致的代码风格和注释规范
6. **测试**: 为关键组件编写单元测试

这份文档提供了 ModuForge-RS 演示应用前端开发的完整技术指导，确保开发过程的一致性和高效性。