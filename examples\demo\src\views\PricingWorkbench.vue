<template>
  <div class="pricing-workbench">
    <!-- 顶部工具栏 -->
    <div class="workbench-toolbar">
      <div class="toolbar-left">
        <a-button @click="goBack" type="text">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回控制台
        </a-button>
        <a-divider type="vertical" />
        <span class="project-title">{{ currentProject?.info?.name || '未命名项目' }}</span>
        <a-tag v-if="!isProjectSaved" color="orange">未保存</a-tag>
      </div>
      
      <div class="toolbar-right">
        <a-button @click="saveProject" :loading="saving">
          <template #icon>
            <SaveOutlined />
          </template>
          保存 (Ctrl+S)
        </a-button>
        <a-button @click="saveProjectAs" :loading="saving">
          另存为
        </a-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="workbench-content">
      <!-- 左侧项目结构树 -->
      <div class="project-tree-panel">
        <div class="panel-header">
          <h4>项目结构</h4>
          <a-dropdown>
            <a-button type="text" size="small">
              <template #icon>
                <PlusOutlined />
              </template>
            </a-button>
            <template #overlay>
              <a-menu @click="handleTreeAction">
                <a-menu-item key="add-single">添加单项工程</a-menu-item>
                <a-menu-item key="add-unit">添加单位工程</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        
        <a-tree
          v-model:selectedKeys="selectedTreeKeys"
          :tree-data="projectTreeData"
          :show-line="true"
          @select="onTreeSelect"
        >
          <template #title="{ title, key, type }">
            <div class="tree-node-title">
              <span>{{ title }}</span>
              <a-dropdown trigger="contextmenu">
                <span></span>
                <template #overlay>
                  <a-menu @click="handleNodeAction($event, key, type)">
                    <a-menu-item key="rename">重命名</a-menu-item>
                    <a-menu-item key="delete" danger>删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
        </a-tree>
      </div>

      <!-- 右侧主内容区 -->
      <div class="main-content-panel">
        <a-tabs v-model:activeKey="activeTab" type="card">
          <a-tab-pane key="overview" tab="项目概况">
            <ProjectOverview 
              v-if="currentProject"
              :project="currentProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="bill-items" tab="分部分项">
            <BillItemsTable 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="measure-items" tab="措施项目">
            <MeasureItemsTable 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="other-items" tab="其他项目">
            <OtherItemsTable 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="cost-summary" tab="费用汇总">
            <CostSummary 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'
import { 
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

// 导入子组件（这些组件需要后续创建）
import ProjectOverview from '../components/pricing/ProjectOverview.vue'
import BillItemsTable from '../components/pricing/BillItemsTable.vue'
import MeasureItemsTable from '../components/pricing/MeasureItemsTable.vue'
import OtherItemsTable from '../components/pricing/OtherItemsTable.vue'
import CostSummary from '../components/pricing/CostSummary.vue'

const router = useRouter()

// 响应式数据
const currentProject = ref(null)
const selectedTreeKeys = ref([])
const activeTab = ref('overview')
const saving = ref(false)
const isProjectSaved = ref(true)

// 计算属性
const projectTreeData = computed(() => {
  if (!currentProject.value) return []
  
  return [{
    title: currentProject.value.info.name,
    key: 'project',
    type: 'project',
    children: currentProject.value.single_projects?.map((singleProject, sIndex) => ({
      title: singleProject.name,
      key: `single-${sIndex}`,
      type: 'single',
      children: singleProject.unit_projects?.map((unitProject, uIndex) => ({
        title: unitProject.name,
        key: `unit-${sIndex}-${uIndex}`,
        type: 'unit'
      })) || []
    })) || []
  }]
})

const currentUnitProject = computed(() => {
  if (!currentProject.value || selectedTreeKeys.value.length === 0) return null
  
  const selectedKey = selectedTreeKeys.value[0]
  if (!selectedKey.startsWith('unit-')) return null
  
  const [, sIndex, uIndex] = selectedKey.split('-').map(Number)
  return currentProject.value.single_projects?.[sIndex]?.unit_projects?.[uIndex] || null
})

// 加载当前项目
const loadCurrentProject = async () => {
  try {
    const project = await invoke('get_current_project')
    if (project) {
      currentProject.value = project
      // 默认选中第一个单位工程
      if (project.single_projects?.[0]?.unit_projects?.[0]) {
        selectedTreeKeys.value = ['unit-0-0']
      }
    } else {
      message.warning('没有当前项目，返回控制台')
      router.push('/pricing-console')
    }
  } catch (error) {
    console.error('加载当前项目失败:', error)
    message.error('加载项目失败')
    router.push('/pricing-console')
  }
}

// 返回控制台
const goBack = () => {
  if (!isProjectSaved.value) {
    // 提示用户保存
    // 这里可以添加确认对话框
  }
  router.push('/pricing-console')
}

// 保存项目
const saveProject = async () => {
  if (!currentProject.value) return
  
  try {
    saving.value = true
    await invoke('save_current_project', {
      projectData: currentProject.value
    })
    message.success('项目保存成功')
    isProjectSaved.value = true
  } catch (error) {
    console.error('保存项目失败:', error)
    message.error(`保存项目失败: ${error}`)
  } finally {
    saving.value = false
  }
}

// 另存为项目
const saveProjectAs = async () => {
  if (!currentProject.value) return
  
  try {
    saving.value = true
    const filePath = await invoke('save_project_as', {
      projectData: currentProject.value
    })
    message.success('项目另存为成功')
    isProjectSaved.value = true
  } catch (error) {
    if (error !== '用户取消了文件保存') {
      console.error('另存为项目失败:', error)
      message.error(`另存为项目失败: ${error}`)
    }
  } finally {
    saving.value = false
  }
}

// 树节点选择
const onTreeSelect = (selectedKeys, info) => {
  selectedTreeKeys.value = selectedKeys
  
  // 根据选择的节点类型切换标签页
  if (selectedKeys.length > 0) {
    const key = selectedKeys[0]
    if (key === 'project') {
      activeTab.value = 'overview'
    } else if (key.startsWith('unit-')) {
      activeTab.value = 'bill-items'
    }
  }
}

// 处理树操作
const handleTreeAction = ({ key }) => {
  // TODO: 实现添加单项工程和单位工程的逻辑
  console.log('Tree action:', key)
}

// 处理节点操作
const handleNodeAction = (event, nodeKey, nodeType) => {
  // TODO: 实现重命名和删除节点的逻辑
  console.log('Node action:', event.key, nodeKey, nodeType)
}

// 项目更新回调
const onProjectUpdate = (updatedProject) => {
  currentProject.value = updatedProject
  isProjectSaved.value = false
}

// 键盘快捷键
const handleKeydown = (event) => {
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    saveProject()
  }
}

// 组件挂载
onMounted(() => {
  loadCurrentProject()
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.pricing-workbench {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.workbench-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.workbench-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.project-tree-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h4 {
  margin: 0;
  color: #333;
}

.main-content-panel {
  flex: 1;
  background: white;
  overflow: hidden;
}

.tree-node-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

:deep(.ant-tree) {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

:deep(.ant-tabs-content-holder) {
  padding: 24px;
  overflow-y: auto;
}
</style>
