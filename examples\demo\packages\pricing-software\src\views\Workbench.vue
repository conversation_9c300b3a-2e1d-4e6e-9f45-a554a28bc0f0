<template>
  <div class="workbench">
    <!-- 使用共享头部组件 -->
    <SimpleHeader
      :title="headerTitle"
      :show-window-controls="true"
      :is-maximized="isMaximized"
      @minimize="onMinimize"
      @maximize="onMaximize"
      @close="onClose"
    >
      <template #right>
        <div class="actions">
          <a-space>
            <a-button @click="goBack" type="text">
              <template #icon>
                <ArrowLeftOutlined />
              </template>
              返回主页
            </a-button>
            <a-tag v-if="!isProjectSaved" color="orange">未保存</a-tag>
            <a-button @click="saveProject" :loading="saving">
              <template #icon>
                <SaveOutlined />
              </template>
              保存 (Ctrl+S)
            </a-button>
            <a-button @click="saveProjectAs" :loading="saving">
              另存为
            </a-button>
          </a-space>
        </div>
      </template>
    </SimpleHeader>

    <!-- 主要内容区域 -->
    <div class="workbench-content" style="flex: 1; overflow: hidden;"">
      <!-- 左侧项目结构树 -->
      <div class="project-tree-panel">
        <div class="panel-header">
          <h4>项目结构</h4>
          <a-dropdown>
            <a-button type="text" size="small">
              <template #icon>
                <PlusOutlined />
              </template>
            </a-button>
            <template #overlay>
              <a-menu @click="handleTreeAction">
                <a-menu-item key="add-single">添加单项工程</a-menu-item>
                <a-menu-item key="add-unit">添加单位工程</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        
        <a-tree
          v-model:selectedKeys="selectedTreeKeys"
          :tree-data="projectTreeData"
          :show-line="true"
          @select="onTreeSelect"
        >
          <template #title="{ title, key, type }">
            <div class="tree-node-title">
              <span>{{ title }}</span>
              <a-dropdown trigger="contextmenu">
                <span></span>
                <template #overlay>
                  <a-menu @click="handleNodeAction($event, key, type)">
                    <a-menu-item key="rename">重命名</a-menu-item>
                    <a-menu-item key="delete" danger>删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
        </a-tree>
      </div>

      <!-- 右侧主内容区 -->
      <div class="main-content-panel">
        <a-tabs v-model:activeKey="activeTab" type="card">
          <a-tab-pane key="overview" tab="项目概况">
            <ProjectOverview 
              v-if="currentProject"
              :project="currentProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="bill-items" tab="分部分项">
            <BillItemsTable 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="measure-items" tab="措施项目">
            <MeasureItemsTable 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="other-items" tab="其他项目">
            <OtherItemsTable 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
          
          <a-tab-pane key="cost-summary" tab="费用汇总">
            <CostSummary 
              v-if="currentUnitProject"
              :unit-project="currentUnitProject"
              @update="onProjectUpdate"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { SimpleHeader, useSimpleWindowManagement } from '@cost-app/shared-components'

// 导入子组件
import ProjectOverview from '../components/ProjectOverview.vue'
import BillItemsTable from '../components/BillItemsTable.vue'
import MeasureItemsTable from '../components/MeasureItemsTable.vue'
import OtherItemsTable from '../components/OtherItemsTable.vue'
import CostSummary from '../components/CostSummary.vue'

const router = useRouter()

// 使用窗口管理
const { isMaximized, onMinimize, onMaximize, onClose } = useSimpleWindowManagement()
const headerTitle = computed(() => {
  const projectName = currentProject.value?.info?.name || '未命名项目'
  return `项目工作台 - ${projectName}`
})

// 响应式数据
const currentProject = ref(null)
const selectedTreeKeys = ref([])
const activeTab = ref('overview')
const saving = ref(false)
const isProjectSaved = ref(true)

// 计算属性
const projectTreeData = computed(() => {
  if (!currentProject.value) return []
  
  return [{
    title: currentProject.value.info.name,
    key: 'project',
    type: 'project',
    children: currentProject.value.single_projects?.map((singleProject, sIndex) => ({
      title: singleProject.name,
      key: `single-${sIndex}`,
      type: 'single',
      children: singleProject.unit_projects?.map((unitProject, uIndex) => ({
        title: unitProject.name,
        key: `unit-${sIndex}-${uIndex}`,
        type: 'unit'
      })) || []
    })) || []
  }]
})

const currentUnitProject = computed(() => {
  if (!currentProject.value || selectedTreeKeys.value.length === 0) return null
  
  const selectedKey = selectedTreeKeys.value[0]
  if (!selectedKey.startsWith('unit-')) return null
  
  const [, sIndex, uIndex] = selectedKey.split('-').map(Number)
  return currentProject.value.single_projects?.[sIndex]?.unit_projects?.[uIndex] || null
})

// 加载当前项目
const loadCurrentProject = async () => {
  try {
    const project = await invoke('get_current_project')
    if (project) {
      currentProject.value = project
      // 默认选中第一个单位工程
      if (project.single_projects?.[0]?.unit_projects?.[0]) {
        selectedTreeKeys.value = ['unit-0-0']
      }
    } else {
      message.warning('没有当前项目，返回主页')
      router.push('/')
    }
  } catch (error) {
    console.error('加载当前项目失败:', error)
    message.error('加载项目失败')
    router.push('/')
  }
}

// 返回主页
const goBack = () => {
  if (!isProjectSaved.value) {
    // 可以添加确认对话框
  }
  router.push('/')
}

// 保存项目
const saveProject = async () => {
  if (!currentProject.value) return
  
  try {
    saving.value = true
    await invoke('save_current_project', {
      projectData: currentProject.value
    })
    message.success('项目保存成功')
    isProjectSaved.value = true
  } catch (error) {
    console.error('保存项目失败:', error)
    message.error(`保存项目失败: ${error}`)
  } finally {
    saving.value = false
  }
}

// 另存为项目
const saveProjectAs = async () => {
  if (!currentProject.value) return
  
  try {
    saving.value = true
    const filePath = await invoke('save_project_as', {
      projectData: currentProject.value
    })
    message.success('项目另存为成功')
    isProjectSaved.value = true
  } catch (error) {
    if (error !== '用户取消了文件保存') {
      console.error('另存为项目失败:', error)
      message.error(`另存为项目失败: ${error}`)
    }
  } finally {
    saving.value = false
  }
}

// 树节点选择
const onTreeSelect = (selectedKeys, info) => {
  selectedTreeKeys.value = selectedKeys
  
  if (selectedKeys.length > 0) {
    const key = selectedKeys[0]
    if (key === 'project') {
      activeTab.value = 'overview'
    } else if (key.startsWith('unit-')) {
      activeTab.value = 'bill-items'
    }
  }
}

// 处理树操作
const handleTreeAction = ({ key }) => {
  console.log('Tree action:', key)
  message.info('功能开发中...')
}

// 处理节点操作
const handleNodeAction = (event, nodeKey, nodeType) => {
  console.log('Node action:', event.key, nodeKey, nodeType)
  message.info('功能开发中...')
}

// 项目更新回调
const onProjectUpdate = (updatedProject) => {
  currentProject.value = updatedProject
  isProjectSaved.value = false
}

// 键盘快捷键
const handleKeydown = (event) => {
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    saveProject()
  }
}

onMounted(() => {
  loadCurrentProject()
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.workbench {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.actions {
  display: flex;
  align-items: center;
}

.workbench-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.project-tree-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h4 {
  margin: 0;
  color: #333;
}

.main-content-panel {
  flex: 1;
  background: white;
  overflow: hidden;
}

.tree-node-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

:deep(.ant-tree) {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

:deep(.ant-tabs-content-holder) {
  padding: 24px;
  overflow-y: auto;
}
</style>
