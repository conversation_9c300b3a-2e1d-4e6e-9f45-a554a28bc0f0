<template>
  <div class="project-overview">
    <a-card title="工程基本信息" class="info-card">
      <a-form
        :model="formData"
        layout="vertical"
        @valuesChange="onFormChange"
      >
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="项目名称">
              <a-input v-model:value="formData.name" placeholder="请输入项目名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="项目编码">
              <a-input v-model:value="formData.code" placeholder="请输入项目编码" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="清单标准">
              <a-select v-model:value="formData.listStandard" placeholder="请选择清单标准">
                <a-select-option value="GB50500-2013">GB50500-2013</a-select-option>
                <a-select-option value="GB50500-2008">GB50500-2008</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="定额标准">
              <a-select v-model:value="formData.quotaStandard" placeholder="请选择定额标准">
                <a-select-option value="地方定额">地方定额</a-select-option>
                <a-select-option value="行业定额">行业定额</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="计税方式">
              <a-radio-group v-model:value="formData.taxMethod">
                <a-radio value="General">一般计税</a-radio>
                <a-radio value="Simple">简易计税</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <a-card title="编制说明" class="info-card">
      <a-textarea
        v-model:value="formData.description"
        :rows="6"
        placeholder="请输入编制说明..."
        @change="onFormChange"
      />
    </a-card>

    <a-card title="工程特征" class="info-card">
      <a-textarea
        v-model:value="formData.characteristics"
        :rows="6"
        placeholder="请输入工程特征..."
        @change="onFormChange"
      />
    </a-card>

    <a-card title="其他信息" class="info-card">
      <div class="other-info-section">
        <div class="other-info-list">
          <div 
            v-for="(value, key) in formData.otherInfo" 
            :key="key"
            class="other-info-item"
          >
            <a-input
              v-model:value="editingOtherInfo[key]?.key"
              placeholder="字段名"
              style="width: 200px; margin-right: 8px;"
              @blur="updateOtherInfoKey(key)"
            />
            <a-input
              v-model:value="formData.otherInfo[key]"
              placeholder="字段值"
              style="flex: 1; margin-right: 8px;"
              @change="onFormChange"
            />
            <a-button 
              type="text" 
              danger
              @click="removeOtherInfo(key)"
            >
              <template #icon>
                <DeleteOutlined />
              </template>
            </a-button>
          </div>
        </div>
        
        <a-button 
          type="dashed" 
          @click="addOtherInfo"
          style="width: 100%; margin-top: 16px;"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          添加字段
        </a-button>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  project: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update'])

// 响应式数据
const formData = reactive({
  name: '',
  code: '',
  listStandard: '',
  quotaStandard: '',
  taxMethod: 'General',
  description: '',
  characteristics: '',
  otherInfo: {}
})

const editingOtherInfo = ref({})

// 初始化表单数据
const initFormData = () => {
  if (props.project?.info) {
    const info = props.project.info
    formData.name = info.name || ''
    formData.code = info.code || ''
    formData.listStandard = info.list_standard || ''
    formData.quotaStandard = info.quota_standard || ''
    formData.taxMethod = info.tax_method || 'General'
    formData.description = info.overview?.description || ''
    formData.characteristics = info.overview?.characteristics || ''
    formData.otherInfo = { ...info.overview?.other_info } || {}
    
    // 初始化编辑状态
    editingOtherInfo.value = {}
    Object.keys(formData.otherInfo).forEach(key => {
      editingOtherInfo.value[key] = { key }
    })
  }
}

// 表单变化处理
const onFormChange = () => {
  const updatedProject = { ...props.project }
  
  // 更新项目基本信息
  updatedProject.info = {
    ...updatedProject.info,
    name: formData.name,
    code: formData.code,
    list_standard: formData.listStandard,
    quota_standard: formData.quotaStandard,
    tax_method: formData.taxMethod,
    overview: {
      ...updatedProject.info.overview,
      description: formData.description,
      characteristics: formData.characteristics,
      other_info: { ...formData.otherInfo }
    }
  }
  
  // 更新修改时间
  updatedProject.updated_at = new Date().toISOString()
  
  emit('update', updatedProject)
}

// 添加其他信息字段
const addOtherInfo = () => {
  const newKey = `字段${Object.keys(formData.otherInfo).length + 1}`
  formData.otherInfo[newKey] = ''
  editingOtherInfo.value[newKey] = { key: newKey }
  onFormChange()
}

// 移除其他信息字段
const removeOtherInfo = (key) => {
  delete formData.otherInfo[key]
  delete editingOtherInfo.value[key]
  onFormChange()
}

// 更新其他信息字段名
const updateOtherInfoKey = (oldKey) => {
  const newKey = editingOtherInfo.value[oldKey]?.key
  if (newKey && newKey !== oldKey && !formData.otherInfo.hasOwnProperty(newKey)) {
    const value = formData.otherInfo[oldKey]
    delete formData.otherInfo[oldKey]
    formData.otherInfo[newKey] = value
    
    delete editingOtherInfo.value[oldKey]
    editingOtherInfo.value[newKey] = { key: newKey }
    
    onFormChange()
  }
}

// 监听项目变化
watch(() => props.project, initFormData, { immediate: true, deep: true })

// 组件挂载
onMounted(() => {
  initFormData()
})
</script>

<style scoped>
.project-overview {
  max-width: 1000px;
}

.info-card {
  margin-bottom: 24px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.other-info-section {
  min-height: 100px;
}

.other-info-list {
  margin-bottom: 16px;
}

.other-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.other-info-item:last-child {
  margin-bottom: 0;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-textarea) {
  resize: vertical;
}
</style>
