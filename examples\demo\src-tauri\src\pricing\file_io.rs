/// 文件 I/O 操作模块
/// 
/// 实现 .ysf 文件格式的读写操作
/// 支持项目的保存、加载和最近打开文件管理

use super::models::PricingProject;
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::fs;
use chrono::{DateTime, Utc};

/// 最近打开的文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentFile {
    /// 文件路径
    pub path: PathBuf,
    /// 项目名称
    pub project_name: String,
    /// 最后打开时间
    pub last_opened: DateTime<Utc>,
}

/// 最近文件管理器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentFilesManager {
    /// 最近文件列表
    pub files: Vec<RecentFile>,
    /// 最大保存数量
    pub max_count: usize,
}

impl Default for RecentFilesManager {
    fn default() -> Self {
        Self {
            files: vec![],
            max_count: 20,
        }
    }
}

impl RecentFilesManager {
    /// 添加最近打开的文件
    pub fn add_recent_file(&mut self, path: PathBuf, project_name: String) {
        // 移除已存在的相同路径
        self.files.retain(|f| f.path != path);
        
        // 添加到列表开头
        self.files.insert(0, RecentFile {
            path,
            project_name,
            last_opened: Utc::now(),
        });
        
        // 保持最大数量限制
        if self.files.len() > self.max_count {
            self.files.truncate(self.max_count);
        }
    }
    
    /// 移除无效的文件路径
    pub fn remove_invalid_files(&mut self) {
        self.files.retain(|f| f.path.exists());
    }
    
    /// 获取最近文件列表
    pub fn get_recent_files(&self) -> &Vec<RecentFile> {
        &self.files
    }
}

/// .ysf 文件操作
pub struct YsfFileHandler;

impl YsfFileHandler {
    /// 保存项目到 .ysf 文件
    pub fn save_project(project: &PricingProject, file_path: &Path) -> Result<()> {
        // 创建目录（如果不存在）
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent)?;
        }
        
        // 序列化项目数据
        let json_data = serde_json::to_string_pretty(project)?;
        
        // 写入文件
        fs::write(file_path, json_data)?;
        
        println!("项目已保存到: {:?}", file_path);
        Ok(())
    }
    
    /// 从 .ysf 文件加载项目
    pub fn load_project(file_path: &Path) -> Result<PricingProject> {
        // 检查文件是否存在
        if !file_path.exists() {
            return Err(anyhow!("文件不存在: {:?}", file_path));
        }
        
        // 读取文件内容
        let file_content = fs::read_to_string(file_path)?;
        
        // 反序列化项目数据
        let mut project: PricingProject = serde_json::from_str(&file_content)?;
        
        // 更新最后修改时间
        project.updated_at = Utc::now();
        
        println!("项目已从文件加载: {:?}", file_path);
        Ok(project)
    }
    
    /// 验证 .ysf 文件格式
    pub fn validate_ysf_file(file_path: &Path) -> Result<bool> {
        if !file_path.exists() {
            return Ok(false);
        }
        
        // 检查文件扩展名
        if file_path.extension().and_then(|s| s.to_str()) != Some("ysf") {
            return Ok(false);
        }
        
        // 尝试解析文件内容
        match Self::load_project(file_path) {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }
}

/// 配置文件管理
pub struct ConfigManager;

impl ConfigManager {
    /// 获取配置目录路径
    pub fn get_config_dir() -> Result<PathBuf> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| anyhow!("无法获取配置目录"))?
            .join("计价软件");
        
        // 确保配置目录存在
        fs::create_dir_all(&config_dir)?;
        
        Ok(config_dir)
    }
    
    /// 获取最近文件配置路径
    pub fn get_recent_files_path() -> Result<PathBuf> {
        Ok(Self::get_config_dir()?.join("recent_files.json"))
    }
    
    /// 保存最近文件列表
    pub fn save_recent_files(manager: &RecentFilesManager) -> Result<()> {
        let path = Self::get_recent_files_path()?;
        let json_data = serde_json::to_string_pretty(manager)?;
        fs::write(path, json_data)?;
        Ok(())
    }
    
    /// 加载最近文件列表
    pub fn load_recent_files() -> Result<RecentFilesManager> {
        let path = Self::get_recent_files_path()?;
        
        if !path.exists() {
            return Ok(RecentFilesManager::default());
        }
        
        let file_content = fs::read_to_string(path)?;
        let mut manager: RecentFilesManager = serde_json::from_str(&file_content)?;
        
        // 清理无效文件
        manager.remove_invalid_files();
        
        Ok(manager)
    }
}

/// 文件对话框辅助函数
pub struct FileDialogHelper;

impl FileDialogHelper {
    /// 获取 .ysf 文件过滤器
    pub fn get_ysf_filter() -> (&'static str, &'static [&'static str]) {
        ("计价软件项目文件", &["ysf"])
    }
    
    /// 获取 XML 文件过滤器
    pub fn get_xml_filter() -> (&'static str, &'static [&'static str]) {
        ("XML 文件", &["xml"])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    
    #[test]
    fn test_save_and_load_project() {
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("test_project.ysf");
        
        // 创建测试项目
        let mut project = PricingProject::default();
        project.info.name = "测试项目".to_string();
        project.info.code = "TEST001".to_string();
        
        // 保存项目
        YsfFileHandler::save_project(&project, &file_path).unwrap();
        
        // 加载项目
        let loaded_project = YsfFileHandler::load_project(&file_path).unwrap();
        
        // 验证数据
        assert_eq!(loaded_project.info.name, "测试项目");
        assert_eq!(loaded_project.info.code, "TEST001");
    }
    
    #[test]
    fn test_recent_files_manager() {
        let mut manager = RecentFilesManager::default();
        let path1 = PathBuf::from("test1.ysf");
        let path2 = PathBuf::from("test2.ysf");
        
        // 添加文件
        manager.add_recent_file(path1.clone(), "项目1".to_string());
        manager.add_recent_file(path2.clone(), "项目2".to_string());
        
        // 验证顺序
        assert_eq!(manager.files.len(), 2);
        assert_eq!(manager.files[0].path, path2);
        assert_eq!(manager.files[1].path, path1);
        
        // 重复添加相同文件
        manager.add_recent_file(path1.clone(), "项目1更新".to_string());
        
        // 验证去重和更新
        assert_eq!(manager.files.len(), 2);
        assert_eq!(manager.files[0].path, path1);
        assert_eq!(manager.files[0].project_name, "项目1更新");
    }
}
